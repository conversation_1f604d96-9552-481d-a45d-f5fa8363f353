import{u as Y,b as L,c as F}from"./VOverlay-C-vrT4h-.js";import{f as K}from"./forwardRefs-B931MWyl.js";import{X as O,Y as E,$ as J,aJ as X,aF as $,bN as j,av as G,r as V,a5 as w,N as H,cJ as Q,aV as U,w as _,D as W,cK as Z,V as q,Z as z,b as o,p as N,a2 as ee,aK as ae,bP as te,an as ne,aM as se,aN as oe,aX as re,ao as ie,cL as le,aW as ue,bR as ce,bt as ve,aQ as me}from"./main-Dk0MC1_J.js";function fe(e){const s=w(e());let t=-1;function a(){clearInterval(t)}function f(){a(),ie(()=>s.value=e())}function d(i){const b=i?getComputedStyle(i):{transitionDuration:.2},l=parseFloat(b.transitionDuration)*1e3||200;if(a(),s.value<=0)return;const k=performance.now();t=window.setInterval(()=>{const y=performance.now()-k+l;s.value=Math.max(e()-y,0),s.value<=0&&a()},l)}return re(a),{clear:a,time:s,start:d,reset:f}}const de=E({multiLine:Boolean,text:String,timer:[Boolean,String],timeout:{type:[Number,String],default:5e3},vertical:Boolean,...oe({location:"bottom"}),...se(),...ne(),...te(),...ae(),...ee(F({transition:"v-snackbar-transition"}),["persistent","noClickAnimation","scrim","scrollStrategy"])},"VSnackbar"),Pe=O()({name:"VSnackbar",props:de(),emits:{"update:modelValue":e=>!0},setup(e,s){let{slots:t}=s;const a=J(e,"modelValue"),{positionClasses:f}=X(e),{scopeId:d}=Y(),{themeClasses:i}=$(e),{colorClasses:b,colorStyles:l,variantClasses:k}=j(e),{roundedClasses:y}=G(e),u=fe(()=>Number(e.timeout)),h=V(),S=V(),c=w(!1),T=w(0),g=V(),R=H(Q,void 0);U(()=>!!R,()=>{const n=le();ue(()=>{g.value=n.mainStyles.value})}),_(a,v),_(()=>e.timeout,v),W(()=>{a.value&&v()});let P=-1;function v(){u.reset(),window.clearTimeout(P);const n=Number(e.timeout);if(!a.value||n===-1)return;const r=Z(S.value);u.start(r),P=window.setTimeout(()=>{a.value=!1},n)}function p(){u.reset(),window.clearTimeout(P)}function A(){c.value=!0,p()}function C(){c.value=!1,v()}function D(n){T.value=n.touches[0].clientY}function M(n){Math.abs(T.value-n.changedTouches[0].clientY)>50&&(a.value=!1)}function B(){c.value&&C()}const I=q(()=>e.location.split(" ").reduce((n,r)=>(n[`v-snackbar--${r}`]=!0,n),{}));return z(()=>{const n=L.filterProps(e),r=!!(t.default||t.text||e.text);return o(L,N({ref:h,class:["v-snackbar",{"v-snackbar--active":a.value,"v-snackbar--multi-line":e.multiLine&&!e.vertical,"v-snackbar--timer":!!e.timer,"v-snackbar--vertical":e.vertical},I.value,f.value,e.class],style:[g.value,e.style]},n,{modelValue:a.value,"onUpdate:modelValue":m=>a.value=m,contentProps:N({class:["v-snackbar__wrapper",i.value,b.value,y.value,k.value],style:[l.value],onPointerenter:A,onPointerleave:C},n.contentProps),persistent:!0,noClickAnimation:!0,scrim:!1,scrollStrategy:"none",_disableGlobalStack:!0,onTouchstartPassive:D,onTouchend:M,onAfterLeave:B},d),{default:()=>{var m,x;return[ce(!1,"v-snackbar"),e.timer&&!c.value&&o("div",{key:"timer",class:"v-snackbar__timer"},[o(ve,{ref:S,color:typeof e.timer=="string"?e.timer:"info",max:e.timeout,"model-value":u.time.value},null)]),r&&o("div",{key:"content",class:"v-snackbar__content",role:"status","aria-live":"polite"},[((m=t.text)==null?void 0:m.call(t))??e.text,(x=t.default)==null?void 0:x.call(t)]),t.actions&&o(me,{defaults:{VBtn:{variant:"text",ripple:!1,slim:!0}}},{default:()=>[o("div",{class:"v-snackbar__actions"},[t.actions({isActive:a})])]})]},activator:t.activator})}),K({},h)}});export{Pe as V};
