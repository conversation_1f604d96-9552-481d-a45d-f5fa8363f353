import{X as b,Y as h,aF as m,bJ as d,Z as v,b as t,aK as f,aL as c,bK as u,aa as g,a7 as x}from"./main-Dk0MC1_J.js";const T=h({fixedHeader:<PERSON><PERSON><PERSON>,fixedFooter:<PERSON><PERSON><PERSON>,height:[Number,String],hover:Boolean,...g(),...u(),...c(),...f()},"VTable"),k=b()({name:"VTable",props:T(),setup(a,r){let{slots:e,emit:y}=r;const{themeClasses:i}=m(a),{densityClasses:n}=d(a);return v(()=>t(a.tag,{class:["v-table",{"v-table--fixed-height":!!a.height,"v-table--fixed-header":a.fixedHeader,"v-table--fixed-footer":a.fixedFooter,"v-table--has-top":!!e.top,"v-table--has-bottom":!!e.bottom,"v-table--hover":a.hover},i.value,n.value,a.class],style:a.style},{default:()=>{var o,s,l;return[(o=e.top)==null?void 0:o.call(e),e.default?t("div",{class:"v-table__wrapper",style:{height:x(a.height)}},[t("table",null,[e.default()])]):(s=e.wrapper)==null?void 0:s.call(e),(l=e.bottom)==null?void 0:l.call(e)]}})),{}}});export{k as V,T as m};
