import{V as o,m as f}from"./VSelectionControl-DnT8_3T3.js";import{X as V,Y as v,$ as c,W as l,Z as I,a2 as k,b as x,p as b,a1 as h}from"./main-Dk0MC1_J.js";const C=v({indeterminate:Boolean,indeterminateIcon:{type:h,default:"$checkboxIndeterminate"},...f({falseIcon:"$checkboxOff",trueIcon:"$checkboxOn"})},"VCheckboxBtn"),B=V()({name:"VCheckboxBtn",props:C(),emits:{"update:modelValue":e=>!0,"update:indeterminate":e=>!0},setup(e,r){let{slots:s}=r;const t=c(e,"indeterminate"),n=c(e,"modelValue");function u(a){t.value&&(t.value=!1)}const i=l(()=>t.value?e.indeterminateIcon:e.falseIcon),m=l(()=>t.value?e.indeterminateIcon:e.trueIcon);return I(()=>{const a=k(o.filterProps(e),["modelValue"]);return x(o,b(a,{modelValue:n.value,"onUpdate:modelValue":[d=>n.value=d,u],class:["v-checkbox-btn",e.class],style:e.style,type:"checkbox",falseIcon:i.value,trueIcon:m.value,"aria-checked":t.value?"mixed":void 0}),s)}),{}}});export{B as V,C as m};
