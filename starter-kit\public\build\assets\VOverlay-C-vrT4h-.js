import{c6 as He,c7 as De,X as Ie,Y,b as _,p as $,T as me,r as W,bx as j,aV as we,c8 as et,c9 as pe,ca as ce,cb as ue,cc as ke,V as R,aU as tt,w as N,aX as D,ao as be,aC as nt,cd as Oe,a7 as H,a6 as Ae,aW as Ee,ce as $e,cf as ot,bU as se,N as We,cg as Pe,bw as at,a5 as Q,aY as rt,D as it,bG as qe,b$ as Ce,W as Te,b0 as st,b_ as lt,L as ct,$ as ut,aF as ft,ak as dt,aw as vt,aH as mt,aT as yt,aZ as gt,Z as ht,ch as wt,H as bt,I as Et,at as xt,F as St,aK as pt,aO as kt,aa as Ot,ci as At}from"./main-Dk0MC1_J.js";import{a as J,b as Pt,s as ye,d as Ct,g as je,n as ze,B as ne,c as Fe}from"./forwardRefs-B931MWyl.js";import{u as Tt,m as Ft}from"./lazy-CwfzxCo7.js";import{M as Lt,m as Bt}from"./VImg-Bo1CDFZl.js";const oe=new WeakMap;function Mt(e,n){Object.keys(n).forEach(t=>{if(He(t)){const a=De(t),o=oe.get(e);if(n[t]==null)o==null||o.forEach(i=>{const[s,r]=i;s===a&&(e.removeEventListener(a,r),o.delete(i))});else if(!o||![...o].some(i=>i[0]===a&&i[1]===n[t])){e.addEventListener(a,n[t]);const i=o||new Set;i.add([a,n[t]]),oe.has(e)||oe.set(e,i)}}else n[t]==null?e.removeAttribute(t):e.setAttribute(t,n[t])})}function Rt(e,n){Object.keys(n).forEach(t=>{if(He(t)){const a=De(t),o=oe.get(e);o==null||o.forEach(i=>{const[s,r]=i;s===a&&(e.removeEventListener(a,r),o.delete(i))})}else e.removeAttribute(t)})}function Ye(e){if(typeof e.getRootNode!="function"){for(;e.parentNode;)e=e.parentNode;return e!==document?null:document}const n=e.getRootNode();return n!==document&&n.getRootNode({composed:!0})!==document?null:n}function Nt(e){let n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1;for(;e;){if(n?Vt(e):xe(e))return e;e=e.parentElement}return document.scrollingElement}function re(e,n){const t=[];if(n&&e&&!n.contains(e))return t;for(;e&&(xe(e)&&t.push(e),e!==n);)e=e.parentElement;return t}function xe(e){if(!e||e.nodeType!==Node.ELEMENT_NODE)return!1;const n=window.getComputedStyle(e);return n.overflowY==="scroll"||n.overflowY==="auto"&&e.scrollHeight>e.clientHeight}function Vt(e){if(!e||e.nodeType!==Node.ELEMENT_NODE)return!1;const n=window.getComputedStyle(e);return["scroll","auto"].includes(n.overflowY)}function _t(e){for(;e;){if(window.getComputedStyle(e).position==="fixed")return!0;e=e.offsetParent}return!1}const Ht=Y({target:[Object,Array]},"v-dialog-transition"),fe=new WeakMap,hn=Ie()({name:"VDialogTransition",props:Ht(),setup(e,n){let{slots:t}=n;const a={onBeforeEnter(o){o.style.pointerEvents="none",o.style.visibility="hidden"},async onEnter(o,i){var b;await new Promise(h=>requestAnimationFrame(h)),await new Promise(h=>requestAnimationFrame(h)),o.style.visibility="";const s=Be(e.target,o),{x:r,y:u,sx:d,sy:l,speed:m}=s;fe.set(o,s);const p=J(o,[{transform:`translate(${r}px, ${u}px) scale(${d}, ${l})`,opacity:0},{}],{duration:225*m,easing:Ct});(b=Le(o))==null||b.forEach(h=>{J(h,[{opacity:0},{opacity:0,offset:.33},{}],{duration:450*m,easing:ye})}),p.finished.then(()=>i())},onAfterEnter(o){o.style.removeProperty("pointer-events")},onBeforeLeave(o){o.style.pointerEvents="none"},async onLeave(o,i){var b;await new Promise(h=>requestAnimationFrame(h));let s;!fe.has(o)||Array.isArray(e.target)||e.target.offsetParent||e.target.getClientRects().length?s=Be(e.target,o):s=fe.get(o);const{x:r,y:u,sx:d,sy:l,speed:m}=s;J(o,[{},{transform:`translate(${r}px, ${u}px) scale(${d}, ${l})`,opacity:0}],{duration:125*m,easing:Pt}).finished.then(()=>i()),(b=Le(o))==null||b.forEach(h=>{J(h,[{},{opacity:0,offset:.2},{opacity:0}],{duration:250*m,easing:ye})})},onAfterLeave(o){o.style.removeProperty("pointer-events")}};return()=>e.target?_(me,$({name:"dialog-transition"},a,{css:!1}),t):_(me,{name:"dialog-transition"},t)}});function Le(e){var t;const n=(t=e.querySelector(":scope > .v-card, :scope > .v-sheet, :scope > .v-list"))==null?void 0:t.children;return n&&[...n]}function Be(e,n){const t=je(e),a=ze(n),[o,i]=getComputedStyle(n).transformOrigin.split(" ").map(f=>parseFloat(f)),[s,r]=getComputedStyle(n).getPropertyValue("--v-overlay-anchor-origin").split(" ");let u=t.left+t.width/2;s==="left"||r==="left"?u-=t.width/2:(s==="right"||r==="right")&&(u+=t.width/2);let d=t.top+t.height/2;s==="top"||r==="top"?d-=t.height/2:(s==="bottom"||r==="bottom")&&(d+=t.height/2);const l=t.width/a.width,m=t.height/a.height,p=Math.max(1,l,m),b=l/p||0,h=m/p||0,T=a.width*a.height/(window.innerWidth*window.innerHeight),F=T>.12?Math.min(1.5,(T-.12)*10+1):1;return{x:u-(o+a.left),y:d-(i+a.top),sx:b,sy:h,speed:F}}function de(e,n){return{x:e.x+n.x,y:e.y+n.y}}function Dt(e,n){return{x:e.x-n.x,y:e.y-n.y}}function Me(e,n){if(e.side==="top"||e.side==="bottom"){const{side:t,align:a}=e,o=a==="left"?0:a==="center"?n.width/2:a==="right"?n.width:a,i=t==="top"?0:t==="bottom"?n.height:t;return de({x:o,y:i},n)}else if(e.side==="left"||e.side==="right"){const{side:t,align:a}=e,o=t==="left"?0:t==="right"?n.width:t,i=a==="top"?0:a==="center"?n.height/2:a==="bottom"?n.height:a;return de({x:o,y:i},n)}return de({x:n.width/2,y:n.height/2},n)}const Xe={static:Wt,connected:jt},It=Y({locationStrategy:{type:[String,Function],default:"static",validator:e=>typeof e=="function"||e in Xe},location:{type:String,default:"bottom"},origin:{type:String,default:"auto"},offset:[Number,String,Array]},"VOverlay-location-strategies");function $t(e,n){const t=W({}),a=W();j&&we(()=>!!(n.isActive.value&&e.locationStrategy),i=>{var s,r;N(()=>e.locationStrategy,i),D(()=>{window.removeEventListener("resize",o),a.value=void 0}),window.addEventListener("resize",o,{passive:!0}),typeof e.locationStrategy=="function"?a.value=(s=e.locationStrategy(n,e,t))==null?void 0:s.updateLocation:a.value=(r=Xe[e.locationStrategy](n,e,t))==null?void 0:r.updateLocation});function o(i){var s;(s=a.value)==null||s.call(a,i)}return{contentStyles:t,updateLocation:a}}function Wt(){}function qt(e,n){const t=ze(e);return n?t.x+=parseFloat(e.style.right||0):t.x-=parseFloat(e.style.left||0),t.y-=parseFloat(e.style.top||0),t}function jt(e,n,t){(Array.isArray(e.target.value)||_t(e.target.value))&&Object.assign(t.value,{position:"fixed",top:0,[e.isRtl.value?"right":"left"]:0});const{preferredAnchor:o,preferredOrigin:i}=et(()=>{const f=pe(n.location,e.isRtl.value),g=n.origin==="overlap"?f:n.origin==="auto"?ce(f):pe(n.origin,e.isRtl.value);return f.side===g.side&&f.align===ue(g).align?{preferredAnchor:ke(f),preferredOrigin:ke(g)}:{preferredAnchor:f,preferredOrigin:g}}),[s,r,u,d]=["minWidth","minHeight","maxWidth","maxHeight"].map(f=>R(()=>{const g=parseFloat(n[f]);return isNaN(g)?1/0:g})),l=R(()=>{if(Array.isArray(n.offset))return n.offset;if(typeof n.offset=="string"){const f=n.offset.split(" ").map(parseFloat);return f.length<2&&f.push(0),f}return typeof n.offset=="number"?[n.offset,0]:[0,0]});let m=!1,p=-1;const b=new tt(4),h=new ResizeObserver(()=>{if(!m)return;if(requestAnimationFrame(g=>{g!==p&&b.clear(),requestAnimationFrame(k=>{p=k})}),b.isFull){const g=b.values();if(nt(g.at(-1),g.at(-3)))return}const f=F();f&&b.push(f.flipped)});N([e.target,e.contentEl],(f,g)=>{let[k,E]=f,[x,B]=g;x&&!Array.isArray(x)&&h.unobserve(x),k&&!Array.isArray(k)&&h.observe(k),B&&h.unobserve(B),E&&h.observe(E)},{immediate:!0}),D(()=>{h.disconnect()});let T=new ne({x:0,y:0,width:0,height:0});function F(){if(m=!1,requestAnimationFrame(()=>m=!0),!e.target.value||!e.contentEl.value)return;(Array.isArray(e.target.value)||e.target.value.offsetParent||e.target.value.getClientRects().length)&&(T=je(e.target.value));const f=qt(e.contentEl.value,e.isRtl.value),g=re(e.contentEl.value),k=12;g.length||(g.push(document.documentElement),e.contentEl.value.style.top&&e.contentEl.value.style.left||(f.x-=parseFloat(document.documentElement.style.getPropertyValue("--v-body-scroll-x")||0),f.y-=parseFloat(document.documentElement.style.getPropertyValue("--v-body-scroll-y")||0)));const E=g.reduce((A,S)=>{const y=S.getBoundingClientRect(),w=new ne({x:S===document.documentElement?0:y.x,y:S===document.documentElement?0:y.y,width:S.clientWidth,height:S.clientHeight});return A?new ne({x:Math.max(A.left,w.left),y:Math.max(A.top,w.top),width:Math.min(A.right,w.right)-Math.max(A.left,w.left),height:Math.min(A.bottom,w.bottom)-Math.max(A.top,w.top)}):w},void 0);E.x+=k,E.y+=k,E.width-=k*2,E.height-=k*2;let x={anchor:o.value,origin:i.value};function B(A){const S=new ne(f),y=Me(A.anchor,T),w=Me(A.origin,S);let{x:M,y:V}=Dt(y,w);switch(A.anchor.side){case"top":V-=l.value[0];break;case"bottom":V+=l.value[0];break;case"left":M-=l.value[0];break;case"right":M+=l.value[0];break}switch(A.anchor.align){case"top":V-=l.value[1];break;case"bottom":V+=l.value[1];break;case"left":M-=l.value[1];break;case"right":M+=l.value[1];break}return S.x+=M,S.y+=V,S.width=Math.min(S.width,u.value),S.height=Math.min(S.height,d.value),{overflows:Fe(S,E),x:M,y:V}}let P=0,c=0;const O={x:0,y:0},z={x:!1,y:!1};let le=-1;for(;!(le++>10);){const{x:A,y:S,overflows:y}=B(x);P+=A,c+=S,f.x+=A,f.y+=S;{const w=Oe(x.anchor),M=y.x.before||y.x.after,V=y.y.before||y.y.after;let U=!1;if(["x","y"].forEach(C=>{if(C==="x"&&M&&!z.x||C==="y"&&V&&!z.y){const I={anchor:{...x.anchor},origin:{...x.origin}},ee=C==="x"?w==="y"?ue:ce:w==="y"?ce:ue;I.anchor=ee(I.anchor),I.origin=ee(I.origin);const{overflows:q}=B(I);(q[C].before<=y[C].before&&q[C].after<=y[C].after||q[C].before+q[C].after<(y[C].before+y[C].after)/2)&&(x=I,U=z[C]=!0)}}),U)continue}y.x.before&&(P+=y.x.before,f.x+=y.x.before),y.x.after&&(P-=y.x.after,f.x-=y.x.after),y.y.before&&(c+=y.y.before,f.y+=y.y.before),y.y.after&&(c-=y.y.after,f.y-=y.y.after);{const w=Fe(f,E);O.x=E.width-w.x.before-w.x.after,O.y=E.height-w.y.before-w.y.after,P+=w.x.before,f.x+=w.x.before,c+=w.y.before,f.y+=w.y.before}break}const X=Oe(x.anchor);return Object.assign(t.value,{"--v-overlay-anchor-origin":`${x.anchor.side} ${x.anchor.align}`,transformOrigin:`${x.origin.side} ${x.origin.align}`,top:H(ve(c)),left:e.isRtl.value?void 0:H(ve(P)),right:e.isRtl.value?H(ve(-P)):void 0,minWidth:H(X==="y"?Math.min(s.value,T.width):s.value),maxWidth:H(Re(Ae(O.x,s.value===1/0?0:s.value,u.value))),maxHeight:H(Re(Ae(O.y,r.value===1/0?0:r.value,d.value)))}),{available:O,contentBox:f,flipped:z}}return N(()=>[o.value,i.value,n.offset,n.minWidth,n.minHeight,n.maxWidth,n.maxHeight],()=>F()),be(()=>{const f=F();if(!f)return;const{available:g,contentBox:k}=f;k.height>g.y&&requestAnimationFrame(()=>{F(),requestAnimationFrame(()=>{F()})})}),{updateLocation:F}}function ve(e){return Math.round(e*devicePixelRatio)/devicePixelRatio}function Re(e){return Math.ceil(e*devicePixelRatio)/devicePixelRatio}let ge=!0;const ie=[];function zt(e){!ge||ie.length?(ie.push(e),he()):(ge=!1,e(),he())}let Ne=-1;function he(){cancelAnimationFrame(Ne),Ne=requestAnimationFrame(()=>{const e=ie.shift();e&&e(),ie.length?he():ge=!0})}const ae={none:null,close:Ut,block:Kt,reposition:Gt},Yt=Y({scrollStrategy:{type:[String,Function],default:"block",validator:e=>typeof e=="function"||e in ae}},"VOverlay-scroll-strategies");function Xt(e,n){if(!j)return;let t;Ee(async()=>{t==null||t.stop(),n.isActive.value&&e.scrollStrategy&&(t=$e(),await new Promise(a=>setTimeout(a)),t.active&&t.run(()=>{var a;typeof e.scrollStrategy=="function"?e.scrollStrategy(n,e,t):(a=ae[e.scrollStrategy])==null||a.call(ae,n,e,t)}))}),D(()=>{t==null||t.stop()})}function Ut(e){function n(t){e.isActive.value=!1}Ue(e.targetEl.value??e.contentEl.value,n)}function Kt(e,n){var s;const t=(s=e.root.value)==null?void 0:s.offsetParent,a=[...new Set([...re(e.targetEl.value,n.contained?t:void 0),...re(e.contentEl.value,n.contained?t:void 0)])].filter(r=>!r.classList.contains("v-overlay-scroll-blocked")),o=window.innerWidth-document.documentElement.offsetWidth,i=(r=>xe(r)&&r)(t||document.documentElement);i&&e.root.value.classList.add("v-overlay--scroll-blocked"),a.forEach((r,u)=>{r.style.setProperty("--v-body-scroll-x",H(-r.scrollLeft)),r.style.setProperty("--v-body-scroll-y",H(-r.scrollTop)),r!==document.documentElement&&r.style.setProperty("--v-scrollbar-offset",H(o)),r.classList.add("v-overlay-scroll-blocked")}),D(()=>{a.forEach((r,u)=>{const d=parseFloat(r.style.getPropertyValue("--v-body-scroll-x")),l=parseFloat(r.style.getPropertyValue("--v-body-scroll-y")),m=r.style.scrollBehavior;r.style.scrollBehavior="auto",r.style.removeProperty("--v-body-scroll-x"),r.style.removeProperty("--v-body-scroll-y"),r.style.removeProperty("--v-scrollbar-offset"),r.classList.remove("v-overlay-scroll-blocked"),r.scrollLeft=-d,r.scrollTop=-l,r.style.scrollBehavior=m}),i&&e.root.value.classList.remove("v-overlay--scroll-blocked")})}function Gt(e,n,t){let a=!1,o=-1,i=-1;function s(r){zt(()=>{var l,m;const u=performance.now();(m=(l=e.updateLocation).value)==null||m.call(l,r),a=(performance.now()-u)/(1e3/60)>2})}i=(typeof requestIdleCallback>"u"?r=>r():requestIdleCallback)(()=>{t.run(()=>{Ue(e.targetEl.value??e.contentEl.value,r=>{a?(cancelAnimationFrame(o),o=requestAnimationFrame(()=>{o=requestAnimationFrame(()=>{s(r)})})):s(r)})})}),D(()=>{typeof cancelIdleCallback<"u"&&cancelIdleCallback(i),cancelAnimationFrame(o)})}function Ue(e,n){const t=[document,...re(e)];t.forEach(a=>{a.addEventListener("scroll",n,{passive:!0})}),D(()=>{t.forEach(a=>{a.removeEventListener("scroll",n)})})}const Zt=Symbol.for("vuetify:v-menu"),Jt=Y({closeDelay:[Number,String],openDelay:[Number,String]},"delay");function Qt(e,n){let t=()=>{};function a(s){t==null||t();const r=Number(s?e.openDelay:e.closeDelay);return new Promise(u=>{t=ot(r,()=>{n==null||n(s),u(s)})})}function o(){return a(!0)}function i(){return a(!1)}return{clearDelay:t,runOpenDelay:o,runCloseDelay:i}}const en=Y({target:[String,Object],activator:[String,Object],activatorProps:{type:Object,default:()=>({})},openOnClick:{type:Boolean,default:void 0},openOnHover:Boolean,openOnFocus:{type:Boolean,default:void 0},closeOnContentClick:Boolean,...Jt()},"VOverlay-activator");function tn(e,n){let{isActive:t,isTop:a,contentEl:o}=n;const i=se("useActivator"),s=W();let r=!1,u=!1,d=!0;const l=R(()=>e.openOnFocus||e.openOnFocus==null&&e.openOnHover),m=R(()=>e.openOnClick||e.openOnClick==null&&!e.openOnHover&&!l.value),{runOpenDelay:p,runCloseDelay:b}=Qt(e,c=>{c===(e.openOnHover&&r||l.value&&u)&&!(e.openOnHover&&t.value&&!a.value)&&(t.value!==c&&(d=!0),t.value=c)}),h=W(),T={onClick:c=>{c.stopPropagation(),s.value=c.currentTarget||c.target,t.value||(h.value=[c.clientX,c.clientY]),t.value=!t.value},onMouseenter:c=>{var O;(O=c.sourceCapabilities)!=null&&O.firesTouchEvents||(r=!0,s.value=c.currentTarget||c.target,p())},onMouseleave:c=>{r=!1,b()},onFocus:c=>{at(c.target,":focus-visible")!==!1&&(u=!0,c.stopPropagation(),s.value=c.currentTarget||c.target,p())},onBlur:c=>{u=!1,c.stopPropagation(),b()}},F=R(()=>{const c={};return m.value&&(c.onClick=T.onClick),e.openOnHover&&(c.onMouseenter=T.onMouseenter,c.onMouseleave=T.onMouseleave),l.value&&(c.onFocus=T.onFocus,c.onBlur=T.onBlur),c}),f=R(()=>{const c={};if(e.openOnHover&&(c.onMouseenter=()=>{r=!0,p()},c.onMouseleave=()=>{r=!1,b()}),l.value&&(c.onFocusin=()=>{u=!0,p()},c.onFocusout=()=>{u=!1,b()}),e.closeOnContentClick){const O=We(Zt,null);c.onClick=()=>{t.value=!1,O==null||O.closeParents()}}return c}),g=R(()=>{const c={};return e.openOnHover&&(c.onMouseenter=()=>{d&&(r=!0,d=!1,p())},c.onMouseleave=()=>{r=!1,b()}),c});N(a,c=>{var O;c&&(e.openOnHover&&!r&&(!l.value||!u)||l.value&&!u&&(!e.openOnHover||!r))&&!((O=o.value)!=null&&O.contains(document.activeElement))&&(t.value=!1)}),N(t,c=>{c||setTimeout(()=>{h.value=void 0})},{flush:"post"});const k=Pe();Ee(()=>{k.value&&be(()=>{s.value=k.el})});const E=Pe(),x=R(()=>e.target==="cursor"&&h.value?h.value:E.value?E.el:Ke(e.target,i)||s.value),B=R(()=>Array.isArray(x.value)?void 0:x.value);let P;return N(()=>!!e.activator,c=>{c&&j?(P=$e(),P.run(()=>{nn(e,i,{activatorEl:s,activatorEvents:F})})):P&&P.stop()},{flush:"post",immediate:!0}),D(()=>{P==null||P.stop()}),{activatorEl:s,activatorRef:k,target:x,targetEl:B,targetRef:E,activatorEvents:F,contentEvents:f,scrimEvents:g}}function nn(e,n,t){let{activatorEl:a,activatorEvents:o}=t;N(()=>e.activator,(u,d)=>{if(d&&u!==d){const l=r(d);l&&s(l)}u&&be(()=>i())},{immediate:!0}),N(()=>e.activatorProps,()=>{i()}),D(()=>{s()});function i(){let u=arguments.length>0&&arguments[0]!==void 0?arguments[0]:r(),d=arguments.length>1&&arguments[1]!==void 0?arguments[1]:e.activatorProps;u&&Mt(u,$(o.value,d))}function s(){let u=arguments.length>0&&arguments[0]!==void 0?arguments[0]:r(),d=arguments.length>1&&arguments[1]!==void 0?arguments[1]:e.activatorProps;u&&Rt(u,$(o.value,d))}function r(){let u=arguments.length>0&&arguments[0]!==void 0?arguments[0]:e.activator;const d=Ke(u,n);return a.value=(d==null?void 0:d.nodeType)===Node.ELEMENT_NODE?d:void 0,a.value}}function Ke(e,n){var a,o;if(!e)return;let t;if(e==="parent"){let i=(o=(a=n==null?void 0:n.proxy)==null?void 0:a.$el)==null?void 0:o.parentNode;for(;i!=null&&i.hasAttribute("data-no-activator");)i=i.parentNode;t=i}else typeof e=="string"?t=document.querySelector(e):"$el"in e?t=e.$el:t=e;return t}function on(){if(!j)return Q(!1);const{ssr:e}=rt();if(e){const n=Q(!1);return it(()=>{n.value=!0}),n}else return Q(!0)}function an(){const n=se("useScopeId").vnode.scopeId;return{scopeId:n?{[n]:""}:void 0}}const Ve=Symbol.for("vuetify:stack"),Z=qe([]);function rn(e,n,t){const a=se("useStack"),o=!t,i=We(Ve,void 0),s=qe({activeChildren:new Set});ct(Ve,s);const r=Q(Number(Ce(n)));we(e,()=>{var m;const l=(m=Z.at(-1))==null?void 0:m[1];r.value=l?l+10:Number(Ce(n)),o&&Z.push([a.uid,r.value]),i==null||i.activeChildren.add(a.uid),D(()=>{if(o){const p=lt(Z).findIndex(b=>b[0]===a.uid);Z.splice(p,1)}i==null||i.activeChildren.delete(a.uid)})});const u=Q(!0);o&&Ee(()=>{var m;const l=((m=Z.at(-1))==null?void 0:m[0])===a.uid;setTimeout(()=>u.value=l)});const d=Te(()=>!s.activeChildren.size);return{globalTop:st(u),localTop:d,stackStyles:Te(()=>({zIndex:r.value}))}}function sn(e){return{teleportTarget:R(()=>{const t=e();if(t===!0||!j)return;const a=t===!1?document.body:typeof t=="string"?document.querySelector(t):t;if(a==null)return;let o=[...a.children].find(i=>i.matches(".v-overlay-container"));return o||(o=document.createElement("div"),o.className="v-overlay-container",a.appendChild(o)),o})}}function ln(){return!0}function Ge(e,n,t){if(!e||Ze(e,t)===!1)return!1;const a=Ye(n);if(typeof ShadowRoot<"u"&&a instanceof ShadowRoot&&a.host===e.target)return!1;const o=(typeof t.value=="object"&&t.value.include||(()=>[]))();return o.push(n),!o.some(i=>i==null?void 0:i.contains(e.target))}function Ze(e,n){return(typeof n.value=="object"&&n.value.closeConditional||ln)(e)}function cn(e,n,t){const a=typeof t.value=="function"?t.value:t.value.handler;e.shadowTarget=e.target,n._clickOutside.lastMousedownWasOutside&&Ge(e,n,t)&&setTimeout(()=>{Ze(e,t)&&a&&a(e)},0)}function _e(e,n){const t=Ye(e);n(document),typeof ShadowRoot<"u"&&t instanceof ShadowRoot&&n(t)}const un={mounted(e,n){const t=o=>cn(o,e,n),a=o=>{e._clickOutside.lastMousedownWasOutside=Ge(o,e,n)};_e(e,o=>{o.addEventListener("click",t,!0),o.addEventListener("mousedown",a,!0)}),e._clickOutside||(e._clickOutside={lastMousedownWasOutside:!1}),e._clickOutside[n.instance.$.uid]={onClick:t,onMousedown:a}},beforeUnmount(e,n){e._clickOutside&&(_e(e,t=>{var i;if(!t||!((i=e._clickOutside)!=null&&i[n.instance.$.uid]))return;const{onClick:a,onMousedown:o}=e._clickOutside[n.instance.$.uid];t.removeEventListener("click",a,!0),t.removeEventListener("mousedown",o,!0)}),delete e._clickOutside[n.instance.$.uid])}};function fn(e){const{modelValue:n,color:t,...a}=e;return _(me,{name:"fade-transition",appear:!0},{default:()=>[e.modelValue&&_("div",$({class:["v-overlay__scrim",e.color.backgroundColorClasses.value],style:e.color.backgroundColorStyles.value},a),null)]})}const dn=Y({absolute:Boolean,attach:[Boolean,String,Object],closeOnBack:{type:Boolean,default:!0},contained:Boolean,contentClass:null,contentProps:null,disabled:Boolean,opacity:[Number,String],noClickAnimation:Boolean,modelValue:Boolean,persistent:Boolean,scrim:{type:[Boolean,String],default:!0},zIndex:{type:[Number,String],default:2e3},...en(),...Ot(),...kt(),...Ft(),...It(),...Yt(),...pt(),...Bt()},"VOverlay"),wn=Ie()({name:"VOverlay",directives:{ClickOutside:un},inheritAttrs:!1,props:{_disableGlobalStack:Boolean,...dn()},emits:{"click:outside":e=>!0,"update:modelValue":e=>!0,keydown:e=>!0,afterEnter:()=>!0,afterLeave:()=>!0},setup(e,n){let{slots:t,attrs:a,emit:o}=n;const i=se("VOverlay"),s=W(),r=W(),u=W(),d=ut(e,"modelValue"),l=R({get:()=>d.value,set:v=>{v&&e.disabled||(d.value=v)}}),{themeClasses:m}=ft(e),{rtlClasses:p,isRtl:b}=dt(),{hasContent:h,onAfterLeave:T}=Tt(e,l),F=vt(()=>typeof e.scrim=="string"?e.scrim:null),{globalTop:f,localTop:g,stackStyles:k}=rn(l,()=>e.zIndex,e._disableGlobalStack),{activatorEl:E,activatorRef:x,target:B,targetEl:P,targetRef:c,activatorEvents:O,contentEvents:z,scrimEvents:le}=tn(e,{isActive:l,isTop:g,contentEl:u}),{teleportTarget:X}=sn(()=>{var K,G,Se;const v=e.attach||e.contained;if(v)return v;const L=((K=E==null?void 0:E.value)==null?void 0:K.getRootNode())||((Se=(G=i.proxy)==null?void 0:G.$el)==null?void 0:Se.getRootNode());return L instanceof ShadowRoot?L:!1}),{dimensionStyles:A}=mt(e),S=on(),{scopeId:y}=an();N(()=>e.disabled,v=>{v&&(l.value=!1)});const{contentStyles:w,updateLocation:M}=$t(e,{isRtl:b,contentEl:u,target:B,isActive:l});Xt(e,{root:s,contentEl:u,targetEl:P,isActive:l,updateLocation:M});function V(v){o("click:outside",v),e.persistent?te():l.value=!1}function U(v){return l.value&&f.value&&(!e.scrim||v.target===r.value||v instanceof MouseEvent&&v.shadowTarget===r.value)}j&&N(l,v=>{v?window.addEventListener("keydown",C):window.removeEventListener("keydown",C)},{immediate:!0}),yt(()=>{j&&window.removeEventListener("keydown",C)});function C(v){var L,K,G;v.key==="Escape"&&f.value&&((L=u.value)!=null&&L.contains(document.activeElement)||o("keydown",v),e.persistent?te():(l.value=!1,(K=u.value)!=null&&K.contains(document.activeElement)&&((G=E.value)==null||G.focus())))}function I(v){v.key==="Escape"&&!f.value||o("keydown",v)}const ee=gt();we(()=>e.closeOnBack,()=>{At(ee,v=>{f.value&&l.value?(v(!1),e.persistent?te():l.value=!1):v()})});const q=W();N(()=>l.value&&(e.absolute||e.contained)&&X.value==null,v=>{if(v){const L=Nt(s.value);L&&L!==document.scrollingElement&&(q.value=L.scrollTop)}});function te(){e.noClickAnimation||u.value&&J(u.value,[{transformOrigin:"center"},{transform:"scale(1.03)"},{transformOrigin:"center"}],{duration:150,easing:ye})}function Je(){o("afterEnter")}function Qe(){T(),o("afterLeave")}return ht(()=>{var v;return _(St,null,[(v=t.activator)==null?void 0:v.call(t,{isActive:l.value,targetRef:c,props:$({ref:x},O.value,e.activatorProps)}),S.value&&h.value&&_(wt,{disabled:!X.value,to:X.value},{default:()=>[_("div",$({class:["v-overlay",{"v-overlay--absolute":e.absolute||e.contained,"v-overlay--active":l.value,"v-overlay--contained":e.contained},m.value,p.value,e.class],style:[k.value,{"--v-overlay-opacity":e.opacity,top:H(q.value)},e.style],ref:s,onKeydown:I},y,a),[_(fn,$({color:F,modelValue:l.value&&!!e.scrim,ref:r},le.value),null),_(Lt,{appear:!0,persisted:!0,transition:e.transition,target:B.value,onAfterEnter:Je,onAfterLeave:Qe},{default:()=>{var L;return[bt(_("div",$({ref:u,class:["v-overlay__content",e.contentClass],style:[A.value,w.value]},z.value,e.contentProps),[(L=t.default)==null?void 0:L.call(t,{isActive:l})]),[[Et,l.value],[xt("click-outside"),{handler:V,closeConditional:U,include:()=>[E.value]}]])]}})])]})])}),{activatorEl:E,scrimEl:r,target:B,animateClick:te,contentEl:u,globalTop:f,localTop:g,updateLocation:M}}});export{Zt as V,Qt as a,wn as b,dn as c,hn as d,Nt as g,Jt as m,an as u};
