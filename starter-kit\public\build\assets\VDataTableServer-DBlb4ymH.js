import{c as U,a as $,b as ee,d as ae,e as te,p as oe,f as le,g as re,i as se,j as de,k as ue,l as ne,n as S,o as T,q as V,r as ie,s as ce,t as me}from"./VDataTable-CoHE9dyS.js";import{X as ge,Y as ve,bE as be,V as m,W as l,aS as pe,Z as Pe,b as o,p as x,F as w,L as fe}from"./main-Dk0MC1_J.js";import{V as B}from"./VTable-Ci0TK2au.js";import{V as ye}from"./VDivider-DLKEidXo.js";const he=ve({itemsLength:{type:[Number,String],required:!0},...me(),...ce(),...ie()},"VDataTableServer"),xe=ge()({name:"VDataTableServer",props:he(),emits:{"update:modelValue":e=>!0,"update:page":e=>!0,"update:itemsPerPage":e=>!0,"update:sortBy":e=>!0,"update:options":e=>!0,"update:expanded":e=>!0,"update:groupBy":e=>!0},setup(e,I){let{attrs:k,slots:a}=I;const{groupBy:d}=U(e),{sortBy:r,multiSort:F,mustSort:R}=$(e),{page:u,itemsPerPage:i}=ee(e),{disableSort:E}=be(e),G=m(()=>parseInt(e.itemsLength,10)),{columns:g,headers:H}=ae(e,{groupBy:d,showSelect:l(()=>e.showSelect),showExpand:l(()=>e.showExpand)}),{items:n}=te(e,g),{toggleSort:v}=ne({sortBy:r,multiSort:F,mustSort:R,page:u}),{opened:L,isGroupOpen:N,toggleGroup:_,extractRows:C}=oe({groupBy:d,sortBy:r,disableSort:E}),{pageCount:q,setItemsPerPage:O}=le({page:u,itemsPerPage:i,itemsLength:G}),{flatItems:b}=re(n,d,L),{isSelected:W,select:j,selectAll:A,toggleSelect:X,someSelected:Y,allSelected:Z}=se(e,{allItems:n,currentPage:n}),{isExpanded:z,toggleExpand:J}=de(e),p=m(()=>C(n.value));ue({page:u,itemsPerPage:i,sortBy:r,groupBy:d,search:l(()=>e.search)}),fe("v-data-table",{toggleSort:v,sortBy:r}),pe({VDataTableRows:{hideNoData:l(()=>e.hideNoData),noDataText:l(()=>e.noDataText),loading:l(()=>e.loading),loadingText:l(()=>e.loadingText)}});const t=m(()=>({page:u.value,itemsPerPage:i.value,sortBy:r.value,pageCount:q.value,toggleSort:v,setItemsPerPage:O,someSelected:Y.value,allSelected:Z.value,isSelected:W,select:j,selectAll:A,toggleSelect:X,isExpanded:z,toggleExpand:J,isGroupOpen:N,toggleGroup:_,items:p.value.map(c=>c.raw),internalItems:p.value,groupedItems:b.value,columns:g.value,headers:H.value}));Pe(()=>{const c=S.filterProps(e),K=T.filterProps(e),M=V.filterProps(e),Q=B.filterProps(e);return o(B,x({class:["v-data-table",{"v-data-table--loading":e.loading},e.class],style:e.style},Q,{fixedHeader:e.fixedHeader||e.sticky}),{top:()=>{var s;return(s=a.top)==null?void 0:s.call(a,t.value)},default:()=>{var s,P,f,y,h,D;return a.default?a.default(t.value):o(w,null,[(s=a.colgroup)==null?void 0:s.call(a,t.value),!e.hideDefaultHeader&&o("thead",{key:"thead",class:"v-data-table__thead",role:"rowgroup"},[o(T,K,a)]),(P=a.thead)==null?void 0:P.call(a,t.value),!e.hideDefaultBody&&o("tbody",{class:"v-data-table__tbody",role:"rowgroup"},[(f=a["body.prepend"])==null?void 0:f.call(a,t.value),a.body?a.body(t.value):o(V,x(k,M,{items:b.value}),a),(y=a["body.append"])==null?void 0:y.call(a,t.value)]),(h=a.tbody)==null?void 0:h.call(a,t.value),(D=a.tfoot)==null?void 0:D.call(a,t.value)])},bottom:()=>a.bottom?a.bottom(t.value):!e.hideDefaultFooter&&o(w,null,[o(ye,null,null),o(S,c,{prepend:a["footer.prepend"]})])})})}});export{xe as V};
