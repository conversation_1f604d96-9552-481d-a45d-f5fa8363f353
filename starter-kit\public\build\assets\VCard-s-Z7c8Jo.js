import{X as o,aa as v,aS as q,Z as m,b as n,Y as p,aL as h,F as C,aD as A,aQ as k,t as P,bK as B,a1 as u,ap as w,aF as G,aG as Q,bN as X,bJ as Y,aH as Z,aq as z,cn as U,aI as W,aJ as $,av as ee,bH as ae,bP as te,aK as ne,bI as de,an as ie,aM as le,aN as se,cA as ce,am as re,aO as ue,aP as oe,H as ve,at as me,p as ye,bR as be,co as fe}from"./main-Dk0MC1_J.js";import{c as ge,V as S}from"./VAvatar-UTcwcVU9.js";import{V as ke}from"./VCardText-C0dgHWNm.js";import{V as pe}from"./VImg-Bo1CDFZl.js";const Ve=o()({name:"VCardActions",props:v(),setup(e,d){let{slots:t}=d;return q({VBtn:{slim:!0,variant:"text"}}),m(()=>{var a;return n("div",{class:["v-card-actions",e.class],style:e.style},[(a=t.default)==null?void 0:a.call(t)])}),{}}}),Ie=p({opacity:[Number,String],...v(),...h()},"VCardSubtitle"),Ce=o()({name:"VCardSubtitle",props:Ie(),setup(e,d){let{slots:t}=d;return m(()=>n(e.tag,{class:["v-card-subtitle",e.class],style:[{"--v-card-subtitle-opacity":e.opacity},e.style]},t)),{}}}),Ae=ge("v-card-title"),Pe=p({appendAvatar:String,appendIcon:u,prependAvatar:String,prependIcon:u,subtitle:{type:[String,Number,Boolean],default:void 0},title:{type:[String,Number,Boolean],default:void 0},...v(),...B()},"VCardItem"),Se=o()({name:"VCardItem",props:Pe(),setup(e,d){let{slots:t}=d;return m(()=>{var s;const a=!!(e.prependAvatar||e.prependIcon),y=!!(a||t.prepend),l=!!(e.appendAvatar||e.appendIcon),b=!!(l||t.append),f=!!(e.title!=null||t.title),g=!!(e.subtitle!=null||t.subtitle);return n("div",{class:["v-card-item",e.class],style:e.style},[y&&n("div",{key:"prepend",class:"v-card-item__prepend"},[t.prepend?n(k,{key:"prepend-defaults",disabled:!a,defaults:{VAvatar:{density:e.density,image:e.prependAvatar},VIcon:{density:e.density,icon:e.prependIcon}}},t.prepend):n(C,null,[e.prependAvatar&&n(S,{key:"prepend-avatar",density:e.density,image:e.prependAvatar},null),e.prependIcon&&n(A,{key:"prepend-icon",density:e.density,icon:e.prependIcon},null)])]),n("div",{class:"v-card-item__content"},[f&&n(Ae,{key:"title"},{default:()=>{var i;return[((i=t.title)==null?void 0:i.call(t))??P(e.title)]}}),g&&n(Ce,{key:"subtitle"},{default:()=>{var i;return[((i=t.subtitle)==null?void 0:i.call(t))??P(e.subtitle)]}}),(s=t.default)==null?void 0:s.call(t)]),b&&n("div",{key:"append",class:"v-card-item__append"},[t.append?n(k,{key:"append-defaults",disabled:!l,defaults:{VAvatar:{density:e.density,image:e.appendAvatar},VIcon:{density:e.density,icon:e.appendIcon}}},t.append):n(C,null,[e.appendIcon&&n(A,{key:"append-icon",density:e.density,icon:e.appendIcon},null),e.appendAvatar&&n(S,{key:"append-avatar",density:e.density,image:e.appendAvatar},null)])])])}),{}}}),he=p({appendAvatar:String,appendIcon:u,disabled:Boolean,flat:Boolean,hover:Boolean,image:String,link:{type:Boolean,default:void 0},prependAvatar:String,prependIcon:u,ripple:{type:[Boolean,Object],default:!0},subtitle:{type:[String,Number,Boolean],default:void 0},text:{type:[String,Number,Boolean],default:void 0},title:{type:[String,Number,Boolean],default:void 0},...oe(),...v(),...B(),...ue(),...re(),...ce(),...se(),...le(),...ie(),...de(),...h(),...ne(),...te({variant:"elevated"})},"VCard"),Ne=o()({name:"VCard",directives:{Ripple:w},props:he(),setup(e,d){let{attrs:t,slots:a}=d;const{themeClasses:y}=G(e),{borderClasses:l}=Q(e),{colorClasses:b,colorStyles:f,variantClasses:g}=X(e),{densityClasses:s}=Y(e),{dimensionStyles:i}=Z(e),{elevationClasses:x}=z(e),{loaderClasses:D}=U(e),{locationStyles:L}=W(e),{positionClasses:N}=$(e),{roundedClasses:T}=ee(e),c=ae(e,t);return m(()=>{const _=e.link!==!1&&c.isLink.value,r=!e.disabled&&e.link!==!1&&(e.link||c.isClickable.value),R=_?"a":e.tag,F=!!(a.title||e.title!=null),H=!!(a.subtitle||e.subtitle!=null),M=F||H,O=!!(a.append||e.appendAvatar||e.appendIcon),E=!!(a.prepend||e.prependAvatar||e.prependIcon),J=!!(a.image||e.image),K=M||E||O,j=!!(a.text||e.text!=null);return ve(n(R,ye({class:["v-card",{"v-card--disabled":e.disabled,"v-card--flat":e.flat,"v-card--hover":e.hover&&!(e.disabled||e.flat),"v-card--link":r},y.value,l.value,b.value,s.value,x.value,D.value,N.value,T.value,g.value,e.class],style:[f.value,i.value,L.value,e.style],onClick:r&&c.navigate,tabindex:e.disabled?-1:void 0},c.linkProps),{default:()=>{var V;return[J&&n("div",{key:"image",class:"v-card__image"},[a.image?n(k,{key:"image-defaults",disabled:!e.image,defaults:{VImg:{cover:!0,src:e.image}}},a.image):n(pe,{key:"image-img",cover:!0,src:e.image},null)]),n(fe,{name:"v-card",active:!!e.loading,color:typeof e.loading=="boolean"?void 0:e.loading},{default:a.loader}),K&&n(Se,{key:"item",prependAvatar:e.prependAvatar,prependIcon:e.prependIcon,title:e.title,subtitle:e.subtitle,appendAvatar:e.appendAvatar,appendIcon:e.appendIcon},{default:a.item,prepend:a.prepend,title:a.title,subtitle:a.subtitle,append:a.append}),j&&n(ke,{key:"text"},{default:()=>{var I;return[((I=a.text)==null?void 0:I.call(a))??e.text]}}),(V=a.default)==null?void 0:V.call(a),a.actions&&n(Ve,null,{default:a.actions}),be(r,"v-card")]}}),[[me("ripple"),r&&e.ripple]])}),{}}});export{Ne as V,Se as a,Ae as b,Ve as c,Ce as d};
