import{u as h,b as u,c as p}from"./VOverlay-C-vrT4h-.js";import{f as O}from"./forwardRefs-B931MWyl.js";import{X as x,Y as T,$ as I,_ as R,W as d,r as k,V as n,p as v,Z as w,b as A,a2 as B}from"./main-Dk0MC1_J.js";const C=T({id:String,interactive:Boolean,text:String,...B(p({closeOnBack:!1,location:"end",locationStrategy:"connected",eager:!0,minWidth:0,offset:10,openOnClick:!1,openOnHover:!0,origin:"auto",scrim:!1,scrollStrategy:"reposition",transition:null}),["absolute","persistent"])},"VTooltip"),G=x()({name:"VTooltip",props:C(),emits:{"update:modelValue":t=>!0},setup(t,m){let{slots:e}=m;const i=I(t,"modelValue"),{scopeId:f}=h(),g=R(),r=d(()=>t.id||`v-tooltip-${g}`),l=k(),V=n(()=>t.location.split(" ").length>1?t.location:t.location+" center"),P=n(()=>t.origin==="auto"||t.origin==="overlap"||t.origin.split(" ").length>1||t.location.split(" ").length>1?t.origin:t.origin+" center"),b=d(()=>t.transition!=null?t.transition:i.value?"scale-transition":"fade-transition"),y=n(()=>v({"aria-describedby":r.value},t.activatorProps));return w(()=>{const S=u.filterProps(t);return A(u,v({ref:l,class:["v-tooltip",{"v-tooltip--interactive":t.interactive},t.class],style:t.style,id:r.value},S,{modelValue:i.value,"onUpdate:modelValue":o=>i.value=o,transition:b.value,absolute:!0,location:V.value,origin:P.value,persistent:!0,role:"tooltip",activatorProps:y.value,_disableGlobalStack:!0},f),{activator:e.activator,default:function(){var c;for(var o=arguments.length,s=new Array(o),a=0;a<o;a++)s[a]=arguments[a];return((c=e.default)==null?void 0:c.call(e,...s))??t.text}})}),O({},l)}});export{G as V};
