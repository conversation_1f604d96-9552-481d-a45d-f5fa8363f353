import{N as X,a5 as _,L as q,bu as ge,b_ as I,r as L,V as p,b$ as at,aR as Se,aT as he,Y as M,$ as ee,bU as nt,W as k,D as lt,b0 as it,X as x,Z as R,b as m,aQ as te,a4 as st,c0 as rt,H as be,I as ut,aL as $,aa as U,a1 as T,ap as ot,bH as ct,w as dt,aF as pe,aG as ke,bN as vt,bJ as Ce,aH as Ie,aq as we,av as Ae,c1 as ft,at as mt,p as ae,bP as Pe,aK as Ve,bI as yt,an as Le,am as Oe,aO as Be,bK as Te,aP as _e,c2 as D,bR as gt,F as ce,aD as de,t as ve,ar as St,aW as ht,c3 as Me,c4 as je,aC as bt,c5 as O,a2 as Ne,aw as pt,aS as kt,bX as Ct}from"./main-Dk0MC1_J.js";import{M as It}from"./VImg-Bo1CDFZl.js";import{c as wt,V as fe}from"./VAvatar-UTcwcVU9.js";import{V as At}from"./VDivider-DLKEidXo.js";const ne=Symbol.for("vuetify:list");function Fe(){const e=X(ne,{hasPrepend:_(!1),updateHasPrepend:()=>null}),l={hasPrepend:_(!1),updateHasPrepend:t=>{t&&(l.hasPrepend.value=t)}};return q(ne,l),e}function xe(){return X(ne,null)}const ie=e=>{const l={activate:t=>{let{id:a,value:i,activated:n}=t;return a=I(a),e&&!i&&n.size===1&&n.has(a)||(i?n.add(a):n.delete(a)),n},in:(t,a,i)=>{let n=new Set;if(t!=null)for(const r of ge(t))n=l.activate({id:r,value:!0,activated:new Set(n),children:a,parents:i});return n},out:t=>Array.from(t)};return l},Ee=e=>{const l=ie(e);return{activate:a=>{let{activated:i,id:n,...r}=a;n=I(n);const u=i.has(n)?new Set([n]):new Set;return l.activate({...r,id:n,activated:u})},in:(a,i,n)=>{let r=new Set;if(a!=null){const u=ge(a);u.length&&(r=l.in(u.slice(0,1),i,n))}return r},out:(a,i,n)=>l.out(a,i,n)}},Pt=e=>{const l=ie(e);return{activate:a=>{let{id:i,activated:n,children:r,...u}=a;return i=I(i),r.has(i)?n:l.activate({id:i,activated:n,children:r,...u})},in:l.in,out:l.out}},Vt=e=>{const l=Ee(e);return{activate:a=>{let{id:i,activated:n,children:r,...u}=a;return i=I(i),r.has(i)?n:l.activate({id:i,activated:n,children:r,...u})},in:l.in,out:l.out}},Lt={open:e=>{let{id:l,value:t,opened:a,parents:i}=e;if(t){const n=new Set;n.add(l);let r=i.get(l);for(;r!=null;)n.add(r),r=i.get(r);return n}else return a.delete(l),a},select:()=>null},De={open:e=>{let{id:l,value:t,opened:a,parents:i}=e;if(t){let n=i.get(l);for(a.add(l);n!=null&&n!==l;)a.add(n),n=i.get(n);return a}else a.delete(l);return a},select:()=>null},Ot={open:De.open,select:e=>{let{id:l,value:t,opened:a,parents:i}=e;if(!t)return a;const n=[];let r=i.get(l);for(;r!=null;)n.push(r),r=i.get(r);return new Set(n)}},se=e=>{const l={select:t=>{let{id:a,value:i,selected:n}=t;if(a=I(a),e&&!i){const r=Array.from(n.entries()).reduce((u,d)=>{let[c,y]=d;return y==="on"&&u.push(c),u},[]);if(r.length===1&&r[0]===a)return n}return n.set(a,i?"on":"off"),n},in:(t,a,i)=>{const n=new Map;for(const r of t||[])l.select({id:r,value:!0,selected:n,children:a,parents:i});return n},out:t=>{const a=[];for(const[i,n]of t.entries())n==="on"&&a.push(i);return a}};return l},Ge=e=>{const l=se(e);return{select:a=>{let{selected:i,id:n,...r}=a;n=I(n);const u=i.has(n)?new Map([[n,i.get(n)]]):new Map;return l.select({...r,id:n,selected:u})},in:(a,i,n)=>a!=null&&a.length?l.in(a.slice(0,1),i,n):new Map,out:(a,i,n)=>l.out(a,i,n)}},Bt=e=>{const l=se(e);return{select:a=>{let{id:i,selected:n,children:r,...u}=a;return i=I(i),r.has(i)?n:l.select({id:i,selected:n,children:r,...u})},in:l.in,out:l.out}},Tt=e=>{const l=Ge(e);return{select:a=>{let{id:i,selected:n,children:r,...u}=a;return i=I(i),r.has(i)?n:l.select({id:i,selected:n,children:r,...u})},in:l.in,out:l.out}},Re=e=>{const l={select:t=>{let{id:a,value:i,selected:n,children:r,parents:u}=t;a=I(a);const d=new Map(n),c=[a];for(;c.length;){const f=c.shift();n.set(I(f),i?"on":"off"),r.has(f)&&c.push(...r.get(f))}let y=I(u.get(a));for(;y;){const f=r.get(y),b=f.every(s=>n.get(I(s))==="on"),S=f.every(s=>!n.has(I(s))||n.get(I(s))==="off");n.set(y,b?"on":S?"off":"indeterminate"),y=I(u.get(y))}return e&&!i&&Array.from(n.entries()).reduce((b,S)=>{let[s,o]=S;return o==="on"&&b.push(s),b},[]).length===0?d:n},in:(t,a,i)=>{let n=new Map;for(const r of t||[])n=l.select({id:r,value:!0,selected:n,children:a,parents:i});return n},out:(t,a)=>{const i=[];for(const[n,r]of t.entries())r==="on"&&!a.has(n)&&i.push(n);return i}};return l},_t=e=>{const l=Re(e);return{select:l.select,in:l.in,out:(a,i,n)=>{const r=[];for(const[u,d]of a.entries())if(d==="on"){if(n.has(u)){const c=n.get(u);if(a.get(c)==="on")continue}r.push(u)}return r}}},G=Symbol.for("vuetify:nested"),$e={id:_(),root:{register:()=>null,unregister:()=>null,parents:L(new Map),children:L(new Map),open:()=>null,openOnSelect:()=>null,activate:()=>null,select:()=>null,activatable:L(!1),selectable:L(!1),opened:L(new Set),activated:L(new Set),selected:L(new Map),selectedValues:L([]),getPath:()=>[]}},Mt=M({activatable:Boolean,selectable:Boolean,activeStrategy:[String,Function,Object],selectStrategy:[String,Function,Object],openStrategy:[String,Object],opened:null,activated:null,selected:null,mandatory:Boolean},"nested"),jt=e=>{let l=!1;const t=L(new Map),a=L(new Map),i=ee(e,"opened",e.opened,s=>new Set(s),s=>[...s.values()]),n=p(()=>{if(typeof e.activeStrategy=="object")return e.activeStrategy;if(typeof e.activeStrategy=="function")return e.activeStrategy(e.mandatory);switch(e.activeStrategy){case"leaf":return Pt(e.mandatory);case"single-leaf":return Vt(e.mandatory);case"independent":return ie(e.mandatory);case"single-independent":default:return Ee(e.mandatory)}}),r=p(()=>{if(typeof e.selectStrategy=="object")return e.selectStrategy;if(typeof e.selectStrategy=="function")return e.selectStrategy(e.mandatory);switch(e.selectStrategy){case"single-leaf":return Tt(e.mandatory);case"leaf":return Bt(e.mandatory);case"independent":return se(e.mandatory);case"single-independent":return Ge(e.mandatory);case"trunk":return _t(e.mandatory);case"classic":default:return Re(e.mandatory)}}),u=p(()=>{if(typeof e.openStrategy=="object")return e.openStrategy;switch(e.openStrategy){case"list":return Ot;case"single":return Lt;case"multiple":default:return De}}),d=ee(e,"activated",e.activated,s=>n.value.in(s,t.value,a.value),s=>n.value.out(s,t.value,a.value)),c=ee(e,"selected",e.selected,s=>r.value.in(s,t.value,a.value),s=>r.value.out(s,t.value,a.value));he(()=>{l=!0});function y(s){const o=[];let v=s;for(;v!=null;)o.unshift(v),v=a.value.get(v);return o}const f=nt("nested"),b=new Set,S={id:_(),root:{opened:i,activatable:k(()=>e.activatable),selectable:k(()=>e.selectable),activated:d,selected:c,selectedValues:p(()=>{const s=[];for(const[o,v]of c.value.entries())v==="on"&&s.push(o);return s}),register:(s,o,v)=>{if(b.has(s)){y(s).map(String).join(" -> "),y(o).concat(s).map(String).join(" -> ");return}else b.add(s);o&&s!==o&&a.value.set(s,o),v&&t.value.set(s,[]),o!=null&&t.value.set(o,[...t.value.get(o)||[],s])},unregister:s=>{if(l)return;b.delete(s),t.value.delete(s);const o=a.value.get(s);if(o){const v=t.value.get(o)??[];t.value.set(o,v.filter(g=>g!==s))}a.value.delete(s)},open:(s,o,v)=>{f.emit("click:open",{id:s,value:o,path:y(s),event:v});const g=u.value.open({id:s,value:o,opened:new Set(i.value),children:t.value,parents:a.value,event:v});g&&(i.value=g)},openOnSelect:(s,o,v)=>{const g=u.value.select({id:s,value:o,selected:new Map(c.value),opened:new Set(i.value),children:t.value,parents:a.value,event:v});g&&(i.value=g)},select:(s,o,v)=>{f.emit("click:select",{id:s,value:o,path:y(s),event:v});const g=r.value.select({id:s,value:o,selected:new Map(c.value),children:t.value,parents:a.value,event:v});g&&(c.value=g),S.root.openOnSelect(s,o,v)},activate:(s,o,v)=>{if(!e.activatable)return S.root.select(s,!0,v);f.emit("click:activate",{id:s,value:o,path:y(s),event:v});const g=n.value.activate({id:s,value:o,activated:new Set(d.value),children:t.value,parents:a.value,event:v});if(g.size!==d.value.size)d.value=g;else{for(const C of g)if(!d.value.has(C)){d.value=g;return}for(const C of d.value)if(!g.has(C)){d.value=g;return}}},children:t,parents:a,getPath:y}};return q(G,S),S.root},Ue=(e,l)=>{const t=X(G,$e),a=Symbol("nested item"),i=p(()=>at(e)??a),n={...t,id:i,open:(r,u)=>t.root.open(i.value,r,u),openOnSelect:(r,u)=>t.root.openOnSelect(i.value,r,u),isOpen:p(()=>t.root.opened.value.has(i.value)),parent:p(()=>t.root.parents.value.get(i.value)),activate:(r,u)=>t.root.activate(i.value,r,u),isActivated:p(()=>t.root.activated.value.has(I(i.value))),select:(r,u)=>t.root.select(i.value,r,u),isSelected:p(()=>t.root.selected.value.get(I(i.value))==="on"),isIndeterminate:p(()=>t.root.selected.value.get(I(i.value))==="indeterminate"),isLeaf:p(()=>!t.root.children.value.get(i.value)),isGroupActivator:t.isGroupActivator};return Se(()=>{!t.isGroupActivator&&t.root.register(i.value,t.id.value,l)}),he(()=>{!t.isGroupActivator&&t.root.unregister(i.value)}),l&&q(G,n),n},Nt=()=>{const e=X(G,$e);q(G,{...e,isGroupActivator:!0})};function Ft(){const e=_(!1);return lt(()=>{window.requestAnimationFrame(()=>{e.value=!0})}),{ssrBootStyles:k(()=>e.value?void 0:{transition:"none !important"}),isBooted:it(e)}}const xt=st({name:"VListGroupActivator",setup(e,l){let{slots:t}=l;return Nt(),()=>{var a;return(a=t.default)==null?void 0:a.call(t)}}}),Et=M({activeColor:String,baseColor:String,color:String,collapseIcon:{type:T,default:"$collapse"},expandIcon:{type:T,default:"$expand"},prependIcon:T,appendIcon:T,fluid:Boolean,subgroup:Boolean,title:String,value:null,...U(),...$()},"VListGroup"),me=x()({name:"VListGroup",props:Et(),setup(e,l){let{slots:t}=l;const{isOpen:a,open:i,id:n}=Ue(()=>e.value,!0),r=p(()=>`v-list-group--id-${String(n.value)}`),u=xe(),{isBooted:d}=Ft();function c(S){var s;S.stopPropagation(),!["INPUT","TEXTAREA"].includes((s=S.target)==null?void 0:s.tagName)&&i(!a.value,S)}const y=p(()=>({onClick:c,class:"v-list-group__header",id:r.value})),f=p(()=>a.value?e.collapseIcon:e.expandIcon),b=p(()=>({VListItem:{active:a.value,activeColor:e.activeColor,baseColor:e.baseColor,color:e.color,prependIcon:e.prependIcon||e.subgroup&&f.value,appendIcon:e.appendIcon||!e.subgroup&&f.value,title:e.title,value:e.value}}));return R(()=>m(e.tag,{class:["v-list-group",{"v-list-group--prepend":u==null?void 0:u.hasPrepend.value,"v-list-group--fluid":e.fluid,"v-list-group--subgroup":e.subgroup,"v-list-group--open":a.value},e.class],style:e.style},{default:()=>[t.activator&&m(te,{defaults:b.value},{default:()=>[m(xt,null,{default:()=>[t.activator({props:y.value,isOpen:a.value})]})]}),m(It,{transition:{component:rt},disabled:!d.value},{default:()=>{var S;return[be(m("div",{class:"v-list-group__items",role:"group","aria-labelledby":r.value},[(S=t.default)==null?void 0:S.call(t)]),[[ut,a.value]])]}})]})),{isOpen:a}}}),Dt=M({opacity:[Number,String],...U(),...$()},"VListItemSubtitle"),Gt=x()({name:"VListItemSubtitle",props:Dt(),setup(e,l){let{slots:t}=l;return R(()=>m(e.tag,{class:["v-list-item-subtitle",e.class],style:[{"--v-list-item-subtitle-opacity":e.opacity},e.style]},t)),{}}}),Rt=wt("v-list-item-title"),$t=M({active:{type:Boolean,default:void 0},activeClass:String,activeColor:String,appendAvatar:String,appendIcon:T,baseColor:String,disabled:Boolean,lines:[Boolean,String],link:{type:Boolean,default:void 0},nav:Boolean,prependAvatar:String,prependIcon:T,ripple:{type:[Boolean,Object],default:!0},slim:Boolean,subtitle:{type:[String,Number,Boolean],default:void 0},title:{type:[String,Number,Boolean],default:void 0},value:null,onClick:D(),onClickOnce:D(),..._e(),...U(),...Te(),...Be(),...Oe(),...Le(),...yt(),...$(),...Ve(),...Pe({variant:"text"})},"VListItem"),ye=x()({name:"VListItem",directives:{Ripple:ot},props:$t(),emits:{click:e=>!0},setup(e,l){let{attrs:t,slots:a,emit:i}=l;const n=ct(e,t),r=p(()=>e.value===void 0?n.href.value:e.value),{activate:u,isActivated:d,select:c,isOpen:y,isSelected:f,isIndeterminate:b,isGroupActivator:S,root:s,parent:o,openOnSelect:v,id:g}=Ue(r,!1),C=xe(),A=p(()=>{var h;return e.active!==!1&&(e.active||((h=n.isActive)==null?void 0:h.value)||(s.activatable.value?d.value:f.value))}),E=k(()=>e.link!==!1&&n.isLink.value),j=p(()=>!!C&&(s.selectable.value||s.activatable.value||e.value!=null)),P=p(()=>!e.disabled&&e.link!==!1&&(e.link||n.isClickable.value||j.value)),z=k(()=>e.rounded||e.nav),W=k(()=>e.color??e.activeColor),J=k(()=>({color:A.value?W.value??e.baseColor:e.baseColor,variant:e.variant}));dt(()=>{var h;return(h=n.isActive)==null?void 0:h.value},h=>{h&&H()}),Se(()=>{var h;(h=n.isActive)!=null&&h.value&&H()});function H(){o.value!=null&&s.open(o.value,!0),v(!0)}const{themeClasses:Q}=pe(e),{borderClasses:B}=ke(e),{colorClasses:w,colorStyles:N,variantClasses:qe}=vt(J),{densityClasses:ze}=Ce(e),{dimensionStyles:We}=Ie(e),{elevationClasses:Je}=we(e),{roundedClasses:Qe}=Ae(z),Ye=k(()=>e.lines?`v-list-item--${e.lines}-line`:void 0),Y=p(()=>({isActive:A.value,select:c,isOpen:y.value,isSelected:f.value,isIndeterminate:b.value}));function Ze(h){var F,K;i("click",h),!["INPUT","TEXTAREA"].includes((F=h.target)==null?void 0:F.tagName)&&P.value&&((K=n.navigate)==null||K.call(n,h),!S&&(s.activatable.value?u(!d.value,h):(s.selectable.value||e.value!=null)&&c(!f.value,h)))}function et(h){const F=h.target;["INPUT","TEXTAREA"].includes(F.tagName)||(h.key==="Enter"||h.key===" ")&&(h.preventDefault(),h.target.dispatchEvent(new MouseEvent("click",h)))}return R(()=>{const h=E.value?"a":e.tag,F=a.title||e.title!=null,K=a.subtitle||e.subtitle!=null,re=!!(e.appendAvatar||e.appendIcon),tt=!!(re||a.append),ue=!!(e.prependAvatar||e.prependIcon),Z=!!(ue||a.prepend);return C==null||C.updateHasPrepend(Z),e.activeColor&&ft("active-color",["color","base-color"]),be(m(h,ae({class:["v-list-item",{"v-list-item--active":A.value,"v-list-item--disabled":e.disabled,"v-list-item--link":P.value,"v-list-item--nav":e.nav,"v-list-item--prepend":!Z&&(C==null?void 0:C.hasPrepend.value),"v-list-item--slim":e.slim,[`${e.activeClass}`]:e.activeClass&&A.value},Q.value,B.value,w.value,ze.value,Je.value,Ye.value,Qe.value,qe.value,e.class],style:[N.value,We.value,e.style],tabindex:P.value?C?-2:0:void 0,"aria-selected":j.value?s.activatable.value?d.value:s.selectable.value?f.value:A.value:void 0,onClick:Ze,onKeydown:P.value&&!E.value&&et},n.linkProps),{default:()=>{var oe;return[gt(P.value||A.value,"v-list-item"),Z&&m("div",{key:"prepend",class:"v-list-item__prepend"},[a.prepend?m(te,{key:"prepend-defaults",disabled:!ue,defaults:{VAvatar:{density:e.density,image:e.prependAvatar},VIcon:{density:e.density,icon:e.prependIcon},VListItemAction:{start:!0}}},{default:()=>{var V;return[(V=a.prepend)==null?void 0:V.call(a,Y.value)]}}):m(ce,null,[e.prependAvatar&&m(fe,{key:"prepend-avatar",density:e.density,image:e.prependAvatar},null),e.prependIcon&&m(de,{key:"prepend-icon",density:e.density,icon:e.prependIcon},null)]),m("div",{class:"v-list-item__spacer"},null)]),m("div",{class:"v-list-item__content","data-no-activator":""},[F&&m(Rt,{key:"title"},{default:()=>{var V;return[((V=a.title)==null?void 0:V.call(a,{title:e.title}))??ve(e.title)]}}),K&&m(Gt,{key:"subtitle"},{default:()=>{var V;return[((V=a.subtitle)==null?void 0:V.call(a,{subtitle:e.subtitle}))??ve(e.subtitle)]}}),(oe=a.default)==null?void 0:oe.call(a,Y.value)]),tt&&m("div",{key:"append",class:"v-list-item__append"},[a.append?m(te,{key:"append-defaults",disabled:!re,defaults:{VAvatar:{density:e.density,image:e.appendAvatar},VIcon:{density:e.density,icon:e.appendIcon},VListItemAction:{end:!0}}},{default:()=>{var V;return[(V=a.append)==null?void 0:V.call(a,Y.value)]}}):m(ce,null,[e.appendIcon&&m(de,{key:"append-icon",density:e.density,icon:e.appendIcon},null),e.appendAvatar&&m(fe,{key:"append-avatar",density:e.density,image:e.appendAvatar},null)]),m("div",{class:"v-list-item__spacer"},null)])]}}),[[mt("ripple"),P.value&&e.ripple]])}),{activate:u,isActivated:d,isGroupActivator:S,isSelected:f,list:C,select:c,root:s,id:g,link:n}}}),Ut=M({color:String,inset:Boolean,sticky:Boolean,title:String,...U(),...$()},"VListSubheader"),Ht=x()({name:"VListSubheader",props:Ut(),setup(e,l){let{slots:t}=l;const{textColorClasses:a,textColorStyles:i}=St(()=>e.color);return R(()=>{const n=!!(t.default||e.title);return m(e.tag,{class:["v-list-subheader",{"v-list-subheader--inset":e.inset,"v-list-subheader--sticky":e.sticky},a.value,e.class],style:[{textColorStyles:i},e.style]},{default:()=>{var r;return[n&&m("div",{class:"v-list-subheader__text"},[((r=t.default)==null?void 0:r.call(t))??e.title])]}})}),{}}}),Kt=M({items:Array,returnObject:Boolean},"VListChildren"),He=x()({name:"VListChildren",props:Kt(),setup(e,l){let{slots:t}=l;return Fe(),()=>{var a,i;return((a=t.default)==null?void 0:a.call(t))??((i=e.items)==null?void 0:i.map(n=>{var b,S;let{children:r,props:u,type:d,raw:c}=n;if(d==="divider")return((b=t.divider)==null?void 0:b.call(t,{props:u}))??m(At,u,null);if(d==="subheader")return((S=t.subheader)==null?void 0:S.call(t,{props:u}))??m(Ht,u,null);const y={subtitle:t.subtitle?s=>{var o;return(o=t.subtitle)==null?void 0:o.call(t,{...s,item:c})}:void 0,prepend:t.prepend?s=>{var o;return(o=t.prepend)==null?void 0:o.call(t,{...s,item:c})}:void 0,append:t.append?s=>{var o;return(o=t.append)==null?void 0:o.call(t,{...s,item:c})}:void 0,title:t.title?s=>{var o;return(o=t.title)==null?void 0:o.call(t,{...s,item:c})}:void 0},f=me.filterProps(u);return r?m(me,ae({value:u==null?void 0:u.value},f),{activator:s=>{let{props:o}=s;const v={...u,...o,value:e.returnObject?c:u.value};return t.header?t.header({props:v}):m(ye,v,y)},default:()=>m(He,{items:r,returnObject:e.returnObject},t)}):t.item?t.item({props:u}):m(ye,ae(u,{value:e.returnObject?c:u.value}),y)}))}}}),Xt=M({items:{type:Array,default:()=>[]},itemTitle:{type:[String,Array,Function],default:"title"},itemValue:{type:[String,Array,Function],default:"value"},itemChildren:{type:[Boolean,String,Array,Function],default:"children"},itemProps:{type:[Boolean,String,Array,Function],default:"props"},returnObject:Boolean,valueComparator:Function},"list-items");function le(e,l){const t=O(l,e.itemTitle,l),a=O(l,e.itemValue,t),i=O(l,e.itemChildren),n=e.itemProps===!0?typeof l=="object"&&l!=null&&!Array.isArray(l)?"children"in l?Ne(l,["children"]):l:void 0:O(l,e.itemProps),r={title:t,value:a,...n};return{title:String(r.title??""),value:r.value,props:r,children:Array.isArray(i)?Ke(e,i):void 0,raw:l}}function Ke(e,l){const t=je(e,["itemTitle","itemValue","itemChildren","itemProps","returnObject","valueComparator"]),a=[];for(const i of l)a.push(le(t,i));return a}function ea(e){const l=p(()=>Ke(e,e.items)),t=p(()=>l.value.some(u=>u.value===null)),a=_(new Map),i=_([]);ht(()=>{const u=l.value,d=new Map,c=[];for(let y=0;y<u.length;y++){const f=u[y];if(Me(f.value)||f.value===null){let b=d.get(f.value);b||(b=[],d.set(f.value,b)),b.push(f)}else c.push(f)}a.value=d,i.value=c});function n(u){const d=a.value,c=l.value,y=i.value,f=t.value,b=e.returnObject,S=!!e.valueComparator,s=e.valueComparator||bt,o=je(e,["itemTitle","itemValue","itemChildren","itemProps","returnObject","valueComparator"]),v=[];e:for(const g of u){if(!f&&g===null)continue;if(b&&typeof g=="string"){v.push(le(o,g));continue}const C=d.get(g);if(S||!C){for(const A of S?c:y)if(s(g,A.value)){v.push(A);continue e}v.push(le(o,g));continue}v.push(...C)}return v}function r(u){return e.returnObject?u.map(d=>{let{raw:c}=d;return c}):u.map(d=>{let{value:c}=d;return c})}return{items:l,transformIn:n,transformOut:r}}function qt(e,l){const t=O(l,e.itemType,"item"),a=Me(l)?l:O(l,e.itemTitle),i=O(l,e.itemValue,void 0),n=O(l,e.itemChildren),r=e.itemProps===!0?Ne(l,["children"]):O(l,e.itemProps),u={title:a,value:i,...r};return{type:t,title:u.title,value:u.value,props:u,children:t==="item"&&n?Xe(e,n):void 0,raw:l}}function Xe(e,l){const t=[];for(const a of l)t.push(qt(e,a));return t}function zt(e){return{items:p(()=>Xe(e,e.items))}}const Wt=M({baseColor:String,activeColor:String,activeClass:String,bgColor:String,disabled:Boolean,expandIcon:T,collapseIcon:T,lines:{type:[Boolean,String],default:"one"},slim:Boolean,nav:Boolean,"onClick:open":D(),"onClick:select":D(),"onUpdate:opened":D(),...Mt({selectStrategy:"single-leaf",openStrategy:"list"}),..._e(),...U(),...Te(),...Be(),...Oe(),itemType:{type:String,default:"type"},...Xt(),...Le(),...$(),...Ve(),...Pe({variant:"text"})},"VList"),ta=x()({name:"VList",props:Wt(),emits:{"update:selected":e=>!0,"update:activated":e=>!0,"update:opened":e=>!0,"click:open":e=>!0,"click:activate":e=>!0,"click:select":e=>!0},setup(e,l){let{slots:t}=l;const{items:a}=zt(e),{themeClasses:i}=pe(e),{backgroundColorClasses:n,backgroundColorStyles:r}=pt(()=>e.bgColor),{borderClasses:u}=ke(e),{densityClasses:d}=Ce(e),{dimensionStyles:c}=Ie(e),{elevationClasses:y}=we(e),{roundedClasses:f}=Ae(e),{children:b,open:S,parents:s,select:o,getPath:v}=jt(e),g=k(()=>e.lines?`v-list--${e.lines}-line`:void 0),C=k(()=>e.activeColor),A=k(()=>e.baseColor),E=k(()=>e.color);Fe(),kt({VListGroup:{activeColor:C,baseColor:A,color:E,expandIcon:k(()=>e.expandIcon),collapseIcon:k(()=>e.collapseIcon)},VListItem:{activeClass:k(()=>e.activeClass),activeColor:C,baseColor:A,color:E,density:k(()=>e.density),disabled:k(()=>e.disabled),lines:k(()=>e.lines),nav:k(()=>e.nav),slim:k(()=>e.slim),variant:k(()=>e.variant)}});const j=_(!1),P=L();function z(w){j.value=!0}function W(w){j.value=!1}function J(w){var N;!j.value&&!(w.relatedTarget&&((N=P.value)!=null&&N.contains(w.relatedTarget)))&&B()}function H(w){const N=w.target;if(!(!P.value||["INPUT","TEXTAREA"].includes(N.tagName))){if(w.key==="ArrowDown")B("next");else if(w.key==="ArrowUp")B("prev");else if(w.key==="Home")B("first");else if(w.key==="End")B("last");else return;w.preventDefault()}}function Q(w){j.value=!0}function B(w){if(P.value)return Ct(P.value,w)}return R(()=>m(e.tag,{ref:P,class:["v-list",{"v-list--disabled":e.disabled,"v-list--nav":e.nav,"v-list--slim":e.slim},i.value,n.value,u.value,d.value,y.value,g.value,f.value,e.class],style:[r.value,c.value,e.style],tabindex:e.disabled?-1:0,role:"listbox","aria-activedescendant":void 0,onFocusin:z,onFocusout:W,onFocus:J,onKeydown:H,onMousedown:Q},{default:()=>[m(He,{items:a.value,returnObject:e.returnObject},t)]})),{open:S,select:o,focus:B,children:b,parents:s,getPath:v}}});export{ta as V,ye as a,Rt as b,Ht as c,Gt as d,ea as e,Xt as m,Ft as u};
