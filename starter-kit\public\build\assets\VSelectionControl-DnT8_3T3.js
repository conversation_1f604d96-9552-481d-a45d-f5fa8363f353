import{Y as h,aK as X,bK as q,aa as _,aC as J,a1 as D,X as R,$ as U,_ as L,W as t,aS as O,Z as N,b as d,L as W,aX as Y,ap as Z,a5 as G,r as z,a0 as Q,p as T,H as p,at as ee,F as le,aD as ae,N as te,bJ as ne,V as b,bu as I,ar as oe,aw as ue,ao as ie,bw as re}from"./main-Dk0MC1_J.js";import{a as ce}from"./VInput-DAZ7rAiX.js";const $=Symbol.for("vuetify:selection-control-group"),j=h({color:String,disabled:{type:Boolean,default:null},defaultsTarget:String,error:Boolean,id:String,inline:Boolean,falseIcon:D,trueIcon:D,ripple:{type:[Boolean,Object],default:!0},multiple:{type:Boolean,default:null},name:String,readonly:{type:<PERSON>olean,default:null},modelValue:null,type:String,valueComparator:{type:Function,default:J},..._(),...q(),...X()},"SelectionControlGroup"),se=h({...j({defaultsTarget:"VSelectionControl"})},"VSelectionControlGroup"),ye=R()({name:"VSelectionControlGroup",props:se(),emits:{"update:modelValue":e=>!0},setup(e,u){let{slots:v}=u;const l=U(e,"modelValue"),n=L(),y=t(()=>e.id||`v-selection-control-group-${n}`),r=t(()=>e.name||y.value),a=new Set;return W($,{modelValue:l,forceUpdate:()=>{a.forEach(o=>o())},onForceUpdate:o=>{a.add(o),Y(()=>{a.delete(o)})}}),O({[e.defaultsTarget]:{color:t(()=>e.color),disabled:t(()=>e.disabled),density:t(()=>e.density),error:t(()=>e.error),inline:t(()=>e.inline),modelValue:l,multiple:t(()=>!!e.multiple||e.multiple==null&&Array.isArray(l.value)),name:r,falseIcon:t(()=>e.falseIcon),trueIcon:t(()=>e.trueIcon),readonly:t(()=>e.readonly),ripple:t(()=>e.ripple),type:t(()=>e.type),valueComparator:t(()=>e.valueComparator)}}),N(()=>{var o;return d("div",{class:["v-selection-control-group",{"v-selection-control-group--inline":e.inline},e.class],style:e.style,role:e.type==="radio"?"radiogroup":void 0},[(o=v.default)==null?void 0:o.call(v)])}),{}}}),de=h({label:String,baseColor:String,trueValue:null,falseValue:null,value:null,..._(),...j()},"VSelectionControl");function ve(e){const u=te($,void 0),{densityClasses:v}=ne(e),l=U(e,"modelValue"),n=b(()=>e.trueValue!==void 0?e.trueValue:e.value!==void 0?e.value:!0),y=b(()=>e.falseValue!==void 0?e.falseValue:!1),r=b(()=>!!e.multiple||e.multiple==null&&Array.isArray(l.value)),a=b({get(){const f=u?u.modelValue.value:l.value;return r.value?I(f).some(i=>e.valueComparator(i,n.value)):e.valueComparator(f,n.value)},set(f){if(e.readonly)return;const i=f?n.value:y.value;let m=i;r.value&&(m=f?[...I(l.value),i]:I(l.value).filter(c=>!e.valueComparator(c,n.value))),u?u.modelValue.value=m:l.value=m}}),{textColorClasses:o,textColorStyles:C}=oe(()=>{if(!(e.error||e.disabled))return a.value?e.color:e.baseColor}),{backgroundColorClasses:V,backgroundColorStyles:S}=ue(()=>a.value&&!e.error&&!e.disabled?e.color:e.baseColor),k=b(()=>a.value?e.trueIcon:e.falseIcon);return{group:u,densityClasses:v,trueValue:n,falseValue:y,model:a,textColorClasses:o,textColorStyles:C,backgroundColorClasses:V,backgroundColorStyles:S,icon:k}}const be=R()({name:"VSelectionControl",directives:{Ripple:Z},inheritAttrs:!1,props:de(),emits:{"update:modelValue":e=>!0},setup(e,u){let{attrs:v,slots:l}=u;const{group:n,densityClasses:y,icon:r,model:a,textColorClasses:o,textColorStyles:C,backgroundColorClasses:V,backgroundColorStyles:S,trueValue:k}=ve(e),f=L(),i=G(!1),m=G(!1),c=z(),g=t(()=>e.id||`input-${f}`),F=t(()=>!e.disabled&&!e.readonly);n==null||n.onForceUpdate(()=>{c.value&&(c.value.checked=a.value)});function x(s){F.value&&(i.value=!0,re(s.target,":focus-visible")!==!1&&(m.value=!0))}function A(){i.value=!1,m.value=!1}function E(s){s.stopPropagation()}function H(s){if(!F.value){c.value&&(c.value.checked=a.value);return}e.readonly&&n&&ie(()=>n.forceUpdate()),a.value=s.target.checked}return N(()=>{var P,w;const s=l.label?l.label({label:e.label,props:{for:g.value}}):e.label,[K,M]=Q(v),B=d("input",T({ref:c,checked:a.value,disabled:!!e.disabled,id:g.value,onBlur:A,onFocus:x,onInput:H,"aria-disabled":!!e.disabled,"aria-label":e.label,type:e.type,value:k.value,name:e.name,"aria-checked":e.type==="checkbox"?a.value:void 0},M),null);return d("div",T({class:["v-selection-control",{"v-selection-control--dirty":a.value,"v-selection-control--disabled":e.disabled,"v-selection-control--error":e.error,"v-selection-control--focused":i.value,"v-selection-control--focus-visible":m.value,"v-selection-control--inline":e.inline},y.value,e.class]},K,{style:e.style}),[d("div",{class:["v-selection-control__wrapper",o.value],style:C.value},[(P=l.default)==null?void 0:P.call(l,{backgroundColorClasses:V,backgroundColorStyles:S}),p(d("div",{class:["v-selection-control__input"]},[((w=l.input)==null?void 0:w.call(l,{model:a,textColorClasses:o,textColorStyles:C,backgroundColorClasses:V,backgroundColorStyles:S,inputNode:B,icon:r.value,props:{onFocus:x,onBlur:A,id:g.value}}))??d(le,null,[r.value&&d(ae,{key:"icon",icon:r.value},null),B])]),[[ee("ripple"),e.ripple&&[!e.disabled&&!e.readonly,null,["center","circle"]]]])]),s&&d(ce,{for:g.value,onClick:E},{default:()=>[s]})])}),{isFocused:i,input:c}}});export{be as V,j as a,ye as b,de as m};
