import{X as P,Y as W,ar as se,r as _,V as w,Z as $,ai as X,b as f,p as I,a2 as z,cM as le,F,cN as O,aF as ie,ak as ue,bo as ce,cu as re,a5 as Y,w as de,W as C,aK as ve,aL as j,aa as D,H as U,at as fe,L as me,N as K,$ as N,cx as he,cy as be,ao as ge,a7 as H,I as ye,bJ as we,aw as xe,aS as Ve,bK as Se,bS as Te}from"./main-Dk0MC1_J.js";import{f as Ce,a as ke,s as Be}from"./forwardRefs-B931MWyl.js";import{u as Ie,m as Pe}from"./lazy-CwfzxCo7.js";import{u as We}from"./VList-DJOZ8Vs3.js";import{M as $e}from"./VImg-Bo1CDFZl.js";import{m as Ee,a as L}from"./VChip-pK0cdZky.js";import{u as _e}from"./VOverlay-C-vrT4h-.js";const R=Symbol.for("vuetify:v-tabs"),Xe=W({fixed:Boolean,sliderColor:String,hideSlider:Boolean,direction:{type:String,default:"horizontal"},...z(le({selectedClass:"v-tab--selected",variant:"text"}),["active","block","flat","location","position","symbol"])},"VTab"),Ye=P()({name:"VTab",props:Xe(),setup(e,o){let{slots:n,attrs:t}=o;const{textColorClasses:s,textColorStyles:l}=se(()=>e.sliderColor),a=_(),v=_(),c=w(()=>e.direction==="horizontal"),h=w(()=>{var b,i;return((i=(b=a.value)==null?void 0:b.group)==null?void 0:i.isSelected.value)??!1});function V(b){var r,u;let{value:i}=b;if(i){const g=(u=(r=a.value)==null?void 0:r.$el.parentElement)==null?void 0:u.querySelector(".v-tab--selected .v-tab__slider"),k=v.value;if(!g||!k)return;const E=getComputedStyle(g).color,S=g.getBoundingClientRect(),T=k.getBoundingClientRect(),d=c.value?"x":"y",m=c.value?"X":"Y",y=c.value?"right":"bottom",x=c.value?"width":"height",ee=S[d],te=T[d],B=ee>te?S[y]-T[y]:S[d]-T[d],ne=Math.sign(B)>0?c.value?"right":"bottom":Math.sign(B)<0?c.value?"left":"top":"center",oe=(Math.abs(B)+(Math.sign(B)<0?S[x]:T[x]))/Math.max(S[x],T[x])||0,ae=S[x]/T[x]||0,M=1.5;ke(k,{backgroundColor:[E,"currentcolor"],transform:[`translate${m}(${B}px) scale${m}(${ae})`,`translate${m}(${B/M}px) scale${m}(${(oe-1)/M+1})`,"none"],transformOrigin:Array(3).fill(ne)},{duration:225,easing:Be})}}return $(()=>{const b=X.filterProps(e);return f(X,I({symbol:R,ref:a,class:["v-tab",e.class],style:e.style,tabindex:h.value?0:-1,role:"tab","aria-selected":String(h.value),active:!1},b,t,{block:e.fixed,maxWidth:e.fixed?300:void 0,"onGroup:selected":V}),{...n,default:()=>{var i;return f(F,null,[((i=n.default)==null?void 0:i.call(n))??e.text,!e.hideSlider&&f("div",{ref:v,class:["v-tab__slider",s.value],style:l.value},null)])}})}),Ce({},a)}}),He=e=>{const{touchstartX:o,touchendX:n,touchstartY:t,touchendY:s}=e,l=.5,a=16;e.offsetX=n-o,e.offsetY=s-t,Math.abs(e.offsetY)<l*Math.abs(e.offsetX)&&(e.left&&n<o-a&&e.left(e),e.right&&n>o+a&&e.right(e)),Math.abs(e.offsetX)<l*Math.abs(e.offsetY)&&(e.up&&s<t-a&&e.up(e),e.down&&s>t+a&&e.down(e))};function Re(e,o){var t;const n=e.changedTouches[0];o.touchstartX=n.clientX,o.touchstartY=n.clientY,(t=o.start)==null||t.call(o,{originalEvent:e,...o})}function Me(e,o){var t;const n=e.changedTouches[0];o.touchendX=n.clientX,o.touchendY=n.clientY,(t=o.end)==null||t.call(o,{originalEvent:e,...o}),He(o)}function Le(e,o){var t;const n=e.changedTouches[0];o.touchmoveX=n.clientX,o.touchmoveY=n.clientY,(t=o.move)==null||t.call(o,{originalEvent:e,...o})}function Ae(){let e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};const o={touchstartX:0,touchstartY:0,touchendX:0,touchendY:0,touchmoveX:0,touchmoveY:0,offsetX:0,offsetY:0,left:e.left,right:e.right,up:e.up,down:e.down,start:e.start,move:e.move,end:e.end};return{touchstart:n=>Re(n,o),touchend:n=>Me(n,o),touchmove:n=>Le(n,o)}}function Ge(e,o){var v;const n=o.value,t=n!=null&&n.parent?e.parentElement:e,s=(n==null?void 0:n.options)??{passive:!0},l=(v=o.instance)==null?void 0:v.$.uid;if(!t||!l)return;const a=Ae(o.value);t._touchHandlers=t._touchHandlers??Object.create(null),t._touchHandlers[l]=a,O(a).forEach(c=>{t.addEventListener(c,a[c],s)})}function ze(e,o){var l,a;const n=(l=o.value)!=null&&l.parent?e.parentElement:e,t=(a=o.instance)==null?void 0:a.$.uid;if(!(n!=null&&n._touchHandlers)||!t)return;const s=n._touchHandlers[t];O(s).forEach(v=>{n.removeEventListener(v,s[v])}),delete n._touchHandlers[t]}const q={mounted:Ge,unmounted:ze},J=Symbol.for("vuetify:v-window"),Z=Symbol.for("vuetify:v-window-group"),Q=W({continuous:Boolean,nextIcon:{type:[Boolean,String,Function,Object],default:"$next"},prevIcon:{type:[Boolean,String,Function,Object],default:"$prev"},reverse:Boolean,showArrows:{type:[Boolean,String],validator:e=>typeof e=="boolean"||e==="hover"},touch:{type:[Object,Boolean],default:void 0},direction:{type:String,default:"horizontal"},modelValue:null,disabled:Boolean,selectedClass:{type:String,default:"v-window-item--active"},mandatory:{type:[Boolean,String],default:"force"},...D(),...j(),...ve()},"VWindow"),A=P()({name:"VWindow",directives:{Touch:q},props:Q(),emits:{"update:modelValue":e=>!0},setup(e,o){let{slots:n}=o;const{themeClasses:t}=ie(e),{isRtl:s}=ue(),{t:l}=ce(),a=re(e,Z),v=_(),c=w(()=>s.value?!e.reverse:e.reverse),h=Y(!1),V=w(()=>{const d=e.direction==="vertical"?"y":"x",y=(c.value?!h.value:h.value)?"-reverse":"";return`v-window-${d}${y}-transition`}),b=Y(0),i=_(void 0),r=w(()=>a.items.value.findIndex(d=>a.selected.value.includes(d.id)));de(r,(d,m)=>{const y=a.items.value.length,x=y-1;y<=2?h.value=d<m:d===x&&m===0?h.value=!0:d===0&&m===x?h.value=!1:h.value=d<m}),me(J,{transition:V,isReversed:h,transitionCount:b,transitionHeight:i,rootRef:v});const u=C(()=>e.continuous||r.value!==0),g=C(()=>e.continuous||r.value!==a.items.value.length-1);function k(){u.value&&a.prev()}function E(){g.value&&a.next()}const S=w(()=>{const d=[],m={icon:s.value?e.nextIcon:e.prevIcon,class:`v-window__${c.value?"right":"left"}`,onClick:a.prev,"aria-label":l("$vuetify.carousel.prev")};d.push(u.value?n.prev?n.prev({props:m}):f(X,m,null):f("div",null,null));const y={icon:s.value?e.prevIcon:e.nextIcon,class:`v-window__${c.value?"left":"right"}`,onClick:a.next,"aria-label":l("$vuetify.carousel.next")};return d.push(g.value?n.next?n.next({props:y}):f(X,y,null):f("div",null,null)),d}),T=w(()=>e.touch===!1?e.touch:{...{left:()=>{c.value?k():E()},right:()=>{c.value?E():k()},start:m=>{let{originalEvent:y}=m;y.stopPropagation()}},...e.touch===!0?{}:e.touch});return $(()=>U(f(e.tag,{ref:v,class:["v-window",{"v-window--show-arrows-on-hover":e.showArrows==="hover"},t.value,e.class],style:e.style},{default:()=>{var d,m;return[f("div",{class:"v-window__container",style:{height:i.value}},[(d=n.default)==null?void 0:d.call(n,{group:a}),e.showArrows!==!1&&f("div",{class:"v-window__controls"},[S.value])]),(m=n.additional)==null?void 0:m.call(n,{group:a})]}}),[[fe("touch"),T.value]])),{group:a}}}),Fe=W({...z(Q(),["continuous","nextIcon","prevIcon","showArrows","touch","mandatory"])},"VTabsWindow"),Oe=P()({name:"VTabsWindow",props:Fe(),emits:{"update:modelValue":e=>!0},setup(e,o){let{slots:n}=o;const t=K(R,null),s=N(e,"modelValue"),l=w({get(){var a;return s.value!=null||!t?s.value:(a=t.items.value.find(v=>t.selected.value.includes(v.id)))==null?void 0:a.value},set(a){s.value=a}});return $(()=>{const a=A.filterProps(e);return f(A,I({_as:"VTabsWindow"},a,{modelValue:l.value,"onUpdate:modelValue":v=>l.value=v,class:["v-tabs-window",e.class],style:e.style,mandatory:!1,touch:!1}),n)}),{}}}),p=W({reverseTransition:{type:[Boolean,String],default:void 0},transition:{type:[Boolean,String],default:void 0},...D(),...be(),...Pe()},"VWindowItem"),G=P()({name:"VWindowItem",directives:{Touch:q},props:p(),emits:{"group:selected":e=>!0},setup(e,o){let{slots:n}=o;const t=K(J),s=he(e,Z),{isBooted:l}=We();if(!t||!s)throw new Error("[Vuetify] VWindowItem must be used inside VWindow");const a=Y(!1),v=w(()=>l.value&&(t.isReversed.value?e.reverseTransition!==!1:e.transition!==!1));function c(){!a.value||!t||(a.value=!1,t.transitionCount.value>0&&(t.transitionCount.value-=1,t.transitionCount.value===0&&(t.transitionHeight.value=void 0)))}function h(){var u;a.value||!t||(a.value=!0,t.transitionCount.value===0&&(t.transitionHeight.value=H((u=t.rootRef.value)==null?void 0:u.clientHeight)),t.transitionCount.value+=1)}function V(){c()}function b(u){a.value&&ge(()=>{!v.value||!a.value||!t||(t.transitionHeight.value=H(u.clientHeight))})}const i=w(()=>{const u=t.isReversed.value?e.reverseTransition:e.transition;return v.value?{name:typeof u!="string"?t.transition.value:u,onBeforeEnter:h,onAfterEnter:c,onEnterCancelled:V,onBeforeLeave:h,onAfterLeave:c,onLeaveCancelled:V,onEnter:b}:!1}),{hasContent:r}=Ie(e,s.isSelected);return $(()=>f($e,{transition:i.value,disabled:!l.value},{default:()=>{var u;return[U(f("div",{class:["v-window-item",s.selectedClass.value,e.class],style:e.style},[r.value&&((u=n.default)==null?void 0:u.call(n))]),[[ye,s.isSelected.value]])]}})),{groupItem:s}}}),je=W({...p()},"VTabsWindowItem"),De=P()({name:"VTabsWindowItem",props:je(),setup(e,o){let{slots:n}=o;return $(()=>{const t=G.filterProps(e);return f(G,I({_as:"VTabsWindowItem"},t,{class:["v-tabs-window-item",e.class],style:e.style}),n)}),{}}});function Ue(e){return e?e.map(o=>Te(o)?o:{text:o,value:o}):[]}const Ke=W({alignTabs:{type:String,default:"start"},color:String,fixedTabs:Boolean,items:{type:Array,default:()=>[]},stacked:Boolean,bgColor:String,grow:Boolean,height:{type:[Number,String],default:void 0},hideSlider:Boolean,sliderColor:String,...Ee({mandatory:"force",selectedClass:"v-tab-item--selected"}),...Se(),...j()},"VTabs"),nt=P()({name:"VTabs",props:Ke(),emits:{"update:modelValue":e=>!0},setup(e,o){let{attrs:n,slots:t}=o;const s=N(e,"modelValue"),l=w(()=>Ue(e.items)),{densityClasses:a}=we(e),{backgroundColorClasses:v,backgroundColorStyles:c}=xe(()=>e.bgColor),{scopeId:h}=_e();return Ve({VTab:{color:C(()=>e.color),direction:C(()=>e.direction),stacked:C(()=>e.stacked),fixed:C(()=>e.fixedTabs),sliderColor:C(()=>e.sliderColor),hideSlider:C(()=>e.hideSlider)}}),$(()=>{const V=L.filterProps(e),b=!!(t.window||e.items.length>0);return f(F,null,[f(L,I(V,{modelValue:s.value,"onUpdate:modelValue":i=>s.value=i,class:["v-tabs",`v-tabs--${e.direction}`,`v-tabs--align-tabs-${e.alignTabs}`,{"v-tabs--fixed-tabs":e.fixedTabs,"v-tabs--grow":e.grow,"v-tabs--stacked":e.stacked},a.value,v.value,e.class],style:[{"--v-tabs-height":H(e.height)},c.value,e.style],role:"tablist",symbol:R},h,n),{default:()=>{var i;return[((i=t.default)==null?void 0:i.call(t))??l.value.map(r=>{var u;return((u=t.tab)==null?void 0:u.call(t,{item:r}))??f(Ye,I(r,{key:r.text,value:r.value}),{default:t[`tab.${r.value}`]?()=>{var g;return(g=t[`tab.${r.value}`])==null?void 0:g.call(t,{item:r})}:void 0})})]}}),b&&f(Oe,I({modelValue:s.value,"onUpdate:modelValue":i=>s.value=i,key:"tabs-window"},h),{default:()=>{var i;return[l.value.map(r=>{var u;return((u=t.item)==null?void 0:u.call(t,{item:r}))??f(De,{value:r.value},{default:()=>{var g;return(g=t[`item.${r.value}`])==null?void 0:g.call(t,{item:r})}})}),(i=t.window)==null?void 0:i.call(t)]}})])}),{}}});export{nt as V,Ye as a,Oe as b,De as c,A as d,G as e};
