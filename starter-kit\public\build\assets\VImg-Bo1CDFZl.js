import{X as $,Y as C,aH as Y,Z as D,b as a,aO as Z,aa as E,V as P,bS as W,O as K,T as Q,p as w,J as p,bT as M,aw as ee,av as te,bU as ne,a5 as h,r as re,w as T,aR as ae,ao as se,aT as ie,W as le,H as F,at as oe,a7 as ue,an as ce,F as de,I as ve}from"./main-Dk0MC1_J.js";function ge(e){return{aspectStyles:P(()=>{const l=Number(e.aspectRatio);return l?{paddingBottom:String(1/l*100)+"%"}:void 0})}}const q=C({aspectRatio:[String,Number],contentClass:null,inline:Boolean,...E(),...Z()},"VResponsive"),H=$()({name:"VResponsive",props:q(),setup(e,l){let{slots:s}=l;const{aspectStyles:n}=ge(e),{dimensionStyles:c}=Y(e);return D(()=>{var d;return a("div",{class:["v-responsive",{"v-responsive--inline":e.inline},e.class],style:[c.value,e.style]},[a("div",{class:"v-responsive__sizer",style:n.value},null),(d=s.additional)==null?void 0:d.call(s),s.default&&a("div",{class:["v-responsive__content",e.contentClass]},[s.default()])])}),{}}}),me=C({transition:{type:null,default:"fade-transition",validator:e=>e!==!0}},"transition"),R=(e,l)=>{let{slots:s}=l;const{transition:n,disabled:c,group:d,...f}=e,{component:v=d?K:Q,...S}=W(n)?n:{};let r;return W(n)?r=w(S,JSON.parse(JSON.stringify({disabled:c,group:d})),f):r=w({name:c||!n?"":n},f),p(v,r,s)};function fe(e,l){if(!M)return;const s=l.modifiers||{},n=l.value,{handler:c,options:d}=typeof n=="object"?n:{handler:n,options:{}},f=new IntersectionObserver(function(){var g;let v=arguments.length>0&&arguments[0]!==void 0?arguments[0]:[],S=arguments.length>1?arguments[1]:void 0;const r=(g=e._observe)==null?void 0:g[l.instance.$.uid];if(!r)return;const i=v.some(b=>b.isIntersecting);c&&(!s.quiet||r.init)&&(!s.once||i||r.init)&&c(i,v,S),i&&s.once?x(e,l):r.init=!0},d);e._observe=Object(e._observe),e._observe[l.instance.$.uid]={init:!1,observer:f},f.observe(e)}function x(e,l){var n;const s=(n=e._observe)==null?void 0:n[l.instance.$.uid];s&&(s.observer.unobserve(e),delete e._observe[l.instance.$.uid])}const Se={mounted:fe,unmounted:x},be=C({absolute:Boolean,alt:String,cover:Boolean,color:String,draggable:{type:[Boolean,String],default:void 0},eager:Boolean,gradient:String,lazySrc:String,options:{type:Object,default:()=>({root:void 0,rootMargin:void 0,threshold:void 0})},sizes:String,src:{type:[String,Object],default:""},crossorigin:String,referrerpolicy:String,srcset:String,position:String,...q(),...E(),...ce(),...me()},"VImg"),ye=$()({name:"VImg",directives:{intersect:Se},props:be(),emits:{loadstart:e=>!0,load:e=>!0,error:e=>!0},setup(e,l){let{emit:s,slots:n}=l;const{backgroundColorClasses:c,backgroundColorStyles:d}=ee(()=>e.color),{roundedClasses:f}=te(e),v=ne("VImg"),S=h(""),r=re(),i=h(e.eager?"loading":"idle"),g=h(),b=h(),u=P(()=>e.src&&typeof e.src=="object"?{src:e.src.src,srcset:e.srcset||e.src.srcset,lazySrc:e.lazySrc||e.src.lazySrc,aspect:Number(e.aspectRatio||e.src.aspect||0)}:{src:e.src,srcset:e.srcset,lazySrc:e.lazySrc,aspect:Number(e.aspectRatio||0)}),_=P(()=>u.value.aspect||g.value/b.value||0);T(()=>e.src,()=>{I(i.value!=="idle")}),T(_,(t,o)=>{!t&&o&&r.value&&y(r.value)}),ae(()=>I());function I(t){if(!(e.eager&&t)&&!(M&&!t&&!e.eager)){if(i.value="loading",u.value.lazySrc){const o=new Image;o.src=u.value.lazySrc,y(o,null)}u.value.src&&se(()=>{var o;s("loadstart",((o=r.value)==null?void 0:o.currentSrc)||u.value.src),setTimeout(()=>{var m;if(!v.isUnmounted)if((m=r.value)!=null&&m.complete){if(r.value.naturalWidth||k(),i.value==="error")return;_.value||y(r.value,null),i.value==="loading"&&V()}else _.value||y(r.value),O()})})}}function V(){var t;v.isUnmounted||(O(),y(r.value),i.value="loaded",s("load",((t=r.value)==null?void 0:t.currentSrc)||u.value.src))}function k(){var t;v.isUnmounted||(i.value="error",s("error",((t=r.value)==null?void 0:t.currentSrc)||u.value.src))}function O(){const t=r.value;t&&(S.value=t.currentSrc||t.src)}let z=-1;ie(()=>{clearTimeout(z)});function y(t){let o=arguments.length>1&&arguments[1]!==void 0?arguments[1]:100;const m=()=>{if(clearTimeout(z),v.isUnmounted)return;const{naturalHeight:j,naturalWidth:U}=t;j||U?(g.value=U,b.value=j):!t.complete&&i.value==="loading"&&o!=null?z=window.setTimeout(m,o):(t.currentSrc.endsWith(".svg")||t.currentSrc.startsWith("data:image/svg+xml"))&&(g.value=1,b.value=1)};m()}const B=le(()=>({"v-img__img--cover":e.cover,"v-img__img--contain":!e.cover})),A=()=>{var m;if(!u.value.src||i.value==="idle")return null;const t=a("img",{class:["v-img__img",B.value],style:{objectPosition:e.position},crossorigin:e.crossorigin,src:u.value.src,srcset:u.value.srcset,alt:e.alt,referrerpolicy:e.referrerpolicy,draggable:e.draggable,sizes:e.sizes,ref:r,onLoad:V,onError:k},null),o=(m=n.sources)==null?void 0:m.call(n);return a(R,{transition:e.transition,appear:!0},{default:()=>[F(o?a("picture",{class:"v-img__picture"},[o,t]):t,[[ve,i.value==="loaded"]])]})},J=()=>a(R,{transition:e.transition},{default:()=>[u.value.lazySrc&&i.value!=="loaded"&&a("img",{class:["v-img__img","v-img__img--preload",B.value],style:{objectPosition:e.position},crossorigin:e.crossorigin,src:u.value.lazySrc,alt:e.alt,referrerpolicy:e.referrerpolicy,draggable:e.draggable},null)]}),L=()=>n.placeholder?a(R,{transition:e.transition,appear:!0},{default:()=>[(i.value==="loading"||i.value==="error"&&!n.error)&&a("div",{class:"v-img__placeholder"},[n.placeholder()])]}):null,G=()=>n.error?a(R,{transition:e.transition,appear:!0},{default:()=>[i.value==="error"&&a("div",{class:"v-img__error"},[n.error()])]}):null,X=()=>e.gradient?a("div",{class:"v-img__gradient",style:{backgroundImage:`linear-gradient(${e.gradient})`}},null):null,N=h(!1);{const t=T(_,o=>{o&&(requestAnimationFrame(()=>{requestAnimationFrame(()=>{N.value=!0})}),t())})}return D(()=>{const t=H.filterProps(e);return F(a(H,w({class:["v-img",{"v-img--absolute":e.absolute,"v-img--booting":!N.value},c.value,f.value,e.class],style:[{width:ue(e.width==="auto"?g.value:e.width)},d.value,e.style]},t,{aspectRatio:_.value,"aria-label":e.alt,role:e.alt?"img":void 0}),{additional:()=>a(de,null,[a(A,null,null),a(J,null,null),a(X,null,null),a(L,null,null),a(G,null,null)]),default:n.default}),[[oe("intersect"),{handler:I,options:e.options},null,{once:!0}]])}),{currentSrc:S,image:r,state:i,naturalWidth:g,naturalHeight:b}}});export{Se as I,R as M,ye as V,me as m};
